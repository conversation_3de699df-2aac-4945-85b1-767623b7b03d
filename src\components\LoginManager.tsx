import { useEffect } from 'react'
import Cookies from 'js-cookie'

/**
 * 登录管理组件
 * 负责处理登录状态更新，避免页面刷新
 */
const LoginManager: React.FC = () => {
  useEffect(() => {
    // 定义全局函数，供外部登录脚本调用
    const updateCookies = () => {
      console.log('登录状态更新：Cookie已更新')

      // 使用 setTimeout 确保 Cookie 已经完全写入
      setTimeout(() => {
        const userInfoString = Cookies.get('userInfo')
        const yudaoToken = Cookies.get('yudaoToken')

        if (userInfoString && yudaoToken) {
          try {
            const userInfo = JSON.parse(userInfoString)
            console.log('立即更新用户信息:', userInfo.userName, userInfo.avatar ? '有头像' : '无头像')

            // 立即触发用户信息更新事件
            window.dispatchEvent(new CustomEvent('userInfoUpdated', {
              detail: { userInfo }
            }))

            // 触发登录状态变化事件
            window.dispatchEvent(new CustomEvent('loginStatusChanged', {
              detail: { type: 'login' }
            }))
          } catch (error) {
            console.error('解析用户信息失败:', error)
            // 即使解析失败也要触发检查
            window.dispatchEvent(new CustomEvent('loginStatusChanged', {
              detail: { type: 'login' }
            }))
          }
        } else {
          console.log('Cookie信息不完整，触发状态检查')
          window.dispatchEvent(new CustomEvent('loginStatusChanged', {
            detail: { type: 'login' }
          }))
        }
      }, 50) // 50ms延迟确保Cookie写入完成
    }

    const userInfoHeader = () => {
      console.log('登录状态更新：用户信息头部已更新')
      // 额外的用户信息更新逻辑
    }

    // 将函数挂载到全局对象
    ;(window as any).updateCookies = updateCookies
    ;(window as any).userInfoHeader = userInfoHeader

    // 监听登录状态变化事件
    const handleLoginStatusChange = (event: CustomEvent) => {
      console.log('检测到登录状态变化:', event.detail)
      
      // 强制重新检查用户信息
      const userInfoString = Cookies.get('userInfo')
      if (userInfoString) {
        console.log('用户已登录，更新应用状态')
        // 触发应用状态更新
        window.dispatchEvent(new CustomEvent('userInfoUpdated', {
          detail: { userInfo: JSON.parse(userInfoString) }
        }))
      }
    }

    window.addEventListener('loginStatusChanged', handleLoginStatusChange as EventListener)

    // 清理函数
    return () => {
      window.removeEventListener('loginStatusChanged', handleLoginStatusChange as EventListener)
      // 可选：清理全局函数（但通常保留以供外部脚本使用）
      // delete (window as any).updateCookies
      // delete (window as any).userInfoHeader
    }
  }, [])

  return null // 这是一个逻辑组件，不渲染任何UI
}

export default LoginManager
