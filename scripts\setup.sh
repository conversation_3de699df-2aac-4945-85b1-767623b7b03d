#!/bin/bash

# NovaX案例展示页面 - SSR版本设置脚本

echo "🚀 开始设置 NovaX 案例展示页面 (SSR版本)..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
node_version=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js版本: $node_version"
    # 检查版本是否 >= 16
    major_version=$(echo $node_version | cut -d'.' -f1 | sed 's/v//')
    if [ "$major_version" -lt 16 ]; then
        echo "❌ 需要Node.js 16或更高版本"
        exit 1
    fi
else
    echo "❌ 未找到Node.js，请先安装Node.js 16+"
    exit 1
fi

# 检查npm版本
echo "📋 检查npm版本..."
npm_version=$(npm -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ npm版本: $npm_version"
else
    echo "❌ 未找到npm"
    exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
if npm install; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 检查环境变量文件
echo "🔧 检查环境变量配置..."
if [ ! -f ".env.local" ]; then
    echo "⚠️  未找到.env.local文件，创建默认配置..."
    cat > .env.local << EOF
# API配置
API_BASE_URL=http://**************:48081

# 应用配置
NEXT_PUBLIC_APP_NAME=NovaX Base
NEXT_PUBLIC_APP_ICON=https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png

# SEO配置
NEXT_PUBLIC_SITE_NAME=NovaX案例展示
NEXT_PUBLIC_SITE_DESCRIPTION=查看NovaX AI助手的对话案例展示

# 开发环境配置
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
EOF
    echo "✅ 已创建默认.env.local文件"
else
    echo "✅ 找到.env.local文件"
fi

# 类型检查
echo "🔍 运行TypeScript类型检查..."
if npm run type-check; then
    echo "✅ 类型检查通过"
else
    echo "⚠️  类型检查有警告，但可以继续"
fi

# 构建测试
echo "🏗️  测试构建..."
if npm run build; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    exit 1
fi

echo ""
echo "🎉 设置完成！"
echo ""
echo "📋 可用命令："
echo "  npm run dev     - 启动开发服务器"
echo "  npm run build   - 构建生产版本"
echo "  npm run start   - 启动生产服务器"
echo "  npm run lint    - 代码检查"
echo ""
echo "🌐 访问地址："
echo "  开发环境: http://localhost:3000/case/demo-001"
echo "  生产环境: http://localhost:3000/case/your-case-id"
echo ""
echo "📚 更多信息请查看 README-SSR.md"
echo ""
echo "🚀 现在可以运行 'npm run dev' 启动开发服务器！"
