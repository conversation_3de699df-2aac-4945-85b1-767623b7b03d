/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基础配置
  reactStrictMode: true,

  // 环境变量
  env: {
    API_BASE_URL: process.env.API_BASE_URL || 'http://192.168.16.243:48081',
  },

  // 图片优化配置
  images: {
    domains: ['img.medsci.cn'],
    formats: ['image/webp', 'image/avif'],
  },

  // 重写规则 - 支持旧的URL格式
  async rewrites() {
    return [
      {
        source: '/case-example.html',
        destination: '/case/demo-001',
      },
      {
        source: '/case/:caseId.html',
        destination: '/case/:caseId',
      },
    ]
  },

  // 重定向规则
  async redirects() {
    return [
      {
        source: '/case-example',
        destination: '/case/demo-001',
        permanent: true,
      },
    ]
  },

  // 确保SSR - 禁用可能导致SPA的功能
  experimental: {
    appDir: false,
  },

  // 页面扩展名
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
}

module.exports = nextConfig
