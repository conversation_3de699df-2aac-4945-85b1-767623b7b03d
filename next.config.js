/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // 环境变量
  env: {
    API_BASE_URL: process.env.API_BASE_URL || 'http://192.168.16.243:48081',
  },

  // 图片优化配置
  images: {
    domains: ['img.medsci.cn'],
    formats: ['image/webp', 'image/avif'],
  },

  // 国际化配置
  i18n: {
    locales: ['zh', 'en'],
    defaultLocale: 'zh',
    localeDetection: false,
  },

  // 重写规则 - 支持旧的URL格式
  async rewrites() {
    return [
      {
        source: '/case-example.html',
        destination: '/case/demo-001',
      },
      {
        source: '/case/:caseId.html',
        destination: '/case/:caseId',
      },
    ]
  },

  // 重定向规则
  async redirects() {
    return [
      {
        source: '/case-example',
        destination: '/case/demo-001',
        permanent: true,
      },
    ]
  },

  // 头部配置 - 安全和SEO
  async headers() {
    return [
      {
        source: '/case/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, stale-while-revalidate=86400',
          },
        ],
      },
    ]
  },

  // Webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 添加自定义loader或插件
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      }
    }

    return config
  },

  // 实验性功能
  experimental: {
    // 启用React 18的并发特性
    concurrentFeatures: true,
    // 启用服务端组件（如果需要）
    serverComponents: false,
  },

  // 压缩配置
  compress: true,

  // 构建输出配置
  output: 'standalone',

  // 页面扩展名
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],

  // 自定义构建目录
  distDir: '.next',

  // 静态文件优化
  optimizeFonts: true,

  // 生产环境优化
  productionBrowserSourceMaps: false,

  // 开发环境配置
  ...(process.env.NODE_ENV === 'development' && {
    // 开发环境特定配置
    typescript: {
      ignoreBuildErrors: false,
    },
    eslint: {
      ignoreDuringBuilds: false,
    },
  }),
}

module.exports = nextConfig
