import React, { useState, useEffect, useRef } from 'react'
import { GetServerSideProps } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import AiResponseRenderer from '../../components/AiResponseRenderer'

// 类型定义
interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isGenerating?: boolean
}

interface QAItem {
  query: string
  answer: string
}

interface CaseData {
  caseId: string
  title: string
  appName: string
  appIcon: string
  conversations: QAItem[]
}

interface CasePageProps {
  caseData: CaseData | null
  error?: string
}

// 打字机效果组件
const TypewriterText: React.FC<{
  text: string
  speed?: number
  onComplete?: () => void
  onContentChange?: () => void
}> = ({ text, speed = 30, onComplete, onContentChange }) => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    if (currentIndex >= text.length) {
      if (onComplete) {
        setTimeout(onComplete, 500)
      }
      return
    }

    const timer = setTimeout(() => {
      setDisplayText(prev => prev + text[currentIndex])
      setCurrentIndex(prev => prev + 1)

      if (onContentChange && (
        currentIndex % 20 === 0 ||
        text[currentIndex] === '\n' ||
        currentIndex === text.length - 1
      )) {
        onContentChange()
      }
    }, speed)

    return () => clearTimeout(timer)
  }, [currentIndex, text, speed, onComplete, onContentChange])

  return (
    <div className="typewriter-container">
      <AiResponseRenderer
        content={displayText + (currentIndex < text.length ? '▊' : '')}
        fontSize="lg"
      />
    </div>
  )
}

// 消息组件
const CaseMessage: React.FC<{
  message: ChatMessage
  onTypingComplete?: (messageId: string) => void
  onContentChange?: () => void
  isInstantMode?: boolean
}> = ({ message, onTypingComplete, onContentChange, isInstantMode = false }) => {
  const handleTypingComplete = () => {
    if (onContentChange) onContentChange()
    setTimeout(() => {
      if (onTypingComplete) onTypingComplete(message.id)
    }, 100)
  }

  if (message.type === 'user') {
    return (
      <div className="mx-auto max-w-4xl relative group mb-6">
        <div className="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="text-gray-800 text-base leading-relaxed">
            <AiResponseRenderer content={message.content} fontSize="base" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mx-auto max-w-4xl relative group mb-6">
      <div className="mr-auto max-w-4xl">
        <div className="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
          {message.isGenerating && !isInstantMode ? (
            <TypewriterText
              text={message.content}
              speed={1}
              onComplete={handleTypingComplete}
              onContentChange={onContentChange}
            />
          ) : (
            <AiResponseRenderer content={message.content} fontSize="lg" />
          )}
        </div>
      </div>
    </div>
  )
}

// 主组件
const CasePage: React.FC<CasePageProps> = ({ caseData, error }) => {
  const router = useRouter()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isInstantMode, setIsInstantMode] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const timersRef = useRef<ReturnType<typeof setTimeout>[]>([])
  const currentMessageIndexRef = useRef(0)
  const allMessagesRef = useRef<ChatMessage[]>([])

  // 清理定时器
  const clearAllTimers = () => {
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current = []
  }

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
  }

  // 初始化消息
  useEffect(() => {
    if (caseData && caseData.conversations.length > 0) {
      const allMessages: ChatMessage[] = []
      
      caseData.conversations.forEach((item, index) => {
        if (item.query?.trim()) {
          allMessages.push({
            id: `user_${index}_${Date.now()}`,
            type: 'user',
            content: item.query.trim(),
            timestamp: new Date()
          })
        }
        
        if (item.answer?.trim()) {
          allMessages.push({
            id: `assistant_${index}_${Date.now()}`,
            type: 'assistant',
            content: item.answer.trim(),
            timestamp: new Date(),
            isGenerating: true
          })
        }
      })
      
      allMessagesRef.current = allMessages
      showMessagesSequentially(allMessages)
    }
  }, [caseData])

  // 显示下一条消息
  const showNextMessage = () => {
    const currentIndex = currentMessageIndexRef.current
    const allMessages = allMessagesRef.current

    if (currentIndex >= allMessages.length) {
      setIsProcessing(false)
      return
    }

    const nextMessage = allMessages[currentIndex]
    currentMessageIndexRef.current++

    setMessages(prev => [...prev, nextMessage])

    if (nextMessage.type === 'user') {
      const timer = setTimeout(() => {
        showNextMessage()
      }, 1000)
      timersRef.current.push(timer)
    }
  }

  // 逐条显示消息
  const showMessagesSequentially = (allMessages: ChatMessage[]) => {
    if (allMessages.length === 0) {
      setIsProcessing(false)
      return
    }

    currentMessageIndexRef.current = 0
    setMessages([])
    setIsProcessing(true)
    showNextMessage()
  }

  // 处理打字机完成
  const handleTypingComplete = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? { ...msg, isGenerating: false } : msg
      )
    )

    const timer = setTimeout(() => {
      showNextMessage()
    }, 1200)
    timersRef.current.push(timer)
  }

  // 切换模式
  const handleToggleMode = () => {
    if (isInstantMode) {
      setIsInstantMode(false)
      if (allMessagesRef.current.length > 0) {
        showMessagesSequentially(allMessagesRef.current)
      }
    } else {
      setIsInstantMode(true)
      clearAllTimers()
      setIsProcessing(false)
      
      if (allMessagesRef.current.length > 0) {
        const instantMessages = allMessagesRef.current.map(msg => ({
          ...msg,
          isGenerating: false
        }))
        setMessages(instantMessages)
      }
    }
  }

  // 返回上一页
  const handleGoBack = () => {
    router.back()
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      clearAllTimers()
    }
  }, [])

  // 错误页面
  if (error || !caseData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">案例未找到</h1>
          <p className="text-gray-600 mb-6">{error || '请检查案例ID是否正确'}</p>
          <button
            onClick={handleGoBack}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{caseData.title} - {caseData.appName}</title>
        <meta name="description" content={`查看 ${caseData.appName} 的AI对话案例：${caseData.title}`} />
        <meta property="og:title" content={`${caseData.title} - ${caseData.appName}`} />
        <meta property="og:description" content={`查看 ${caseData.appName} 的AI对话案例`} />
        <meta property="og:type" content="article" />
        
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Article",
              "headline": caseData.title,
              "description": `${caseData.appName} AI对话案例展示`,
              "author": {
                "@type": "Organization",
                "name": caseData.appName
              },
              "publisher": {
                "@type": "Organization",
                "name": caseData.appName,
                "logo": {
                  "@type": "ImageObject",
                  "url": caseData.appIcon
                }
              }
            })
          }}
        />
      </Head>

      <div className="relative h-screen bg-gray-50">
        <div className="h-screen flex flex-col">
          {/* 移动端头部 */}
          <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
            <div className="flex items-center">
              <button
                onClick={handleGoBack}
                className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center flex-1">
                <div className="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3">
                  <img src={caseData.appIcon} alt={caseData.appName} className="w-5 h-5 rounded-full" />
                </div>
                <span className="text-lg font-semibold text-gray-800">{caseData.appName}</span>
              </div>
              <button
                onClick={handleToggleMode}
                className="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200"
              >
                <span>{isInstantMode ? '查看过程' : '查看结果'}</span>
              </button>
            </div>
          </div>

          {/* 桌面端头部 */}
          <div className="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1">
                      <img src={caseData.appIcon} alt={caseData.appName} className="w-6 h-6 rounded-full" />
                    </div>
                    <span className="text-xs text-gray-600 font-medium text-center block w-full">{caseData.appName}</span>
                  </div>
                  <div>
                    <h1 className="text-lg font-semibold text-gray-800">{caseData.title}</h1>
                    <p className="text-sm">案例ID: <span className="text-blue-600 font-medium">{caseData.caseId}</span></p>
                  </div>
                </div>
                <button
                  onClick={handleToggleMode}
                  className="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md"
                >
                  <span>{isInstantMode ? '查看过程' : '查看结果'}</span>
                </button>
              </div>
            </div>
          </div>

          {/* 消息列表区域 */}
          <div className="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6">
            <div className="max-w-4xl mx-auto">
              {/* 服务端预渲染的静态内容 - SEO友好 */}
              <noscript>
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-800">AI对话案例</h2>
                  {caseData.conversations.map((item, index) => (
                    <div key={index} className="space-y-4">
                      {item.query && (
                        <div className="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl p-4">
                          <div className="text-gray-800">{item.query}</div>
                        </div>
                      )}
                      {item.answer && (
                        <div className="mr-auto max-w-4xl">
                          <div className="text-gray-800 prose prose-base max-w-none">
                            <div className="whitespace-pre-wrap">{item.answer}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </noscript>

              {/* 动态消息列表 */}
              <div className="js-only">
                {messages.map((message) => (
                  <CaseMessage
                    key={message.id}
                    message={message}
                    onTypingComplete={handleTypingComplete}
                    onContentChange={scrollToBottom}
                    isInstantMode={isInstantMode}
                  />
                ))}
              </div>
              <div ref={messagesEndRef} />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

// 服务端数据获取
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { caseId } = context.params!
  
  try {
    // 在服务端调用API获取数据
    const response = await fetch(`${process.env.API_BASE_URL}/ai-base/index/qa-list?locale=en`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        articleId: "",
        encryptionId: caseId
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data && Array.isArray(data) && data.length > 0) {
      const firstItem = data[0]
      if (firstItem && firstItem.answer) {
        let answerData = firstItem.answer

        if (typeof answerData === 'string') {
          try {
            answerData = JSON.parse(answerData)
          } catch (parseError) {
            throw new Error('Invalid JSON in answer field')
          }
        }

        if (Array.isArray(answerData) && answerData.length > 0) {
          const caseData: CaseData = {
            caseId: caseId as string,
            title: firstItem.question || '案例展示',
            appName: 'NovaX Base', // 可以从API获取
            appIcon: 'https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png',
            conversations: answerData
          }

          return {
            props: {
              caseData
            }
          }
        }
      }
    }

    throw new Error('Invalid data format')

  } catch (error) {
    console.error('Failed to fetch case data:', error)
    
    return {
      props: {
        caseData: null,
        error: '案例数据加载失败'
      }
    }
  }
}

export default CasePage
