<!DOCTYPE html>
<!--
案例展示页面 - 动态数据版本

使用方法：
1. 修改 API_CONFIG 中的 baseUrl 为你的实际API地址
2. 确保API返回的数据格式符合预期（见下方注释）
3. 通过URL参数传递案例ID：?caseId=your-case-id
4. 如果需要认证，在 API_CONFIG.headers 中添加 Authorization

API数据格式要求：
{
  "caseId": "demo-001",
  "title": "案例标题",
  "appName": "应用名称",
  "appIcon": "图标URL",
  "conversations": [
    {
      "query": "用户问题",
      "answer": "AI回答（支持Markdown格式）"
    }
  ]
}
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案例展示 - NovaX</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --bg-main: #f8fafc;
        }
        
        .typewriter-cursor {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .prose {
            max-width: none;
        }
        
        .prose h1, .prose h2, .prose h3 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        .prose p {
            margin-bottom: 1em;
        }
        
        .prose code {
            background-color: #f1f5f9;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
        }
        
        .prose pre {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .prose blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #64748b;
        }
        
        .prose ul, .prose ol {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }
        
        .prose li {
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app" class="relative h-screen">
        <div class="h-screen flex flex-col">
            <!-- 移动端头部 -->
            <div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
                <div class="flex items-center">
                    <button onclick="goBack()" class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <div class="flex items-center flex-1">
                        <div class="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3">
                            <img id="mobile-app-icon" src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" alt="AI" class="w-5 h-5 rounded-full">
                        </div>
                        <span id="mobile-app-name" class="text-lg font-semibold text-gray-800">NovaX Base</span>
                    </div>
                    <button onclick="toggleMode()" id="mobile-toggle-btn" class="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200">
                        <span>查看结果</span>
                    </button>
                </div>
            </div>

            <!-- 桌面端头部 -->
            <div class="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1">
                                    <img id="desktop-app-icon" src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" alt="AI" class="w-6 h-6 rounded-full">
                                </div>
                                <span id="desktop-app-name" class="text-xs text-gray-600 font-medium text-center block w-full">NovaX Base</span>
                            </div>
                            <div>
                                <h1 id="case-title" class="text-lg font-semibold text-gray-800">案例展示</h1>
                                <p class="text-sm">案例ID: <span id="case-id" class="text-blue-600 font-medium">demo-001</span></p>
                            </div>
                        </div>
                        <button onclick="toggleMode()" id="desktop-toggle-btn" class="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md">
                            <span>查看结果</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 消息列表区域 -->
            <div class="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6">
                <div class="max-w-4xl mx-auto" id="messages-container">
                    <!-- 消息将在这里动态生成 -->
                </div>
                <div id="messages-end"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isInstantMode = false;
        let isProcessing = false;
        let currentMessageIndex = 0;
        let allMessages = [];
        let timers = [];
        
        // API配置
        const API_CONFIG = {
            baseUrl: 'http://**************:48081/ai-base/index/qa-list?locale=en', // 替换为实际的API地址
            endpoints: {
                getCaseData: '/api/cases/{caseId}', // 获取案例数据
                getCaseList: '/api/cases' // 获取案例列表
            },
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': 'Bearer your-token-here' // 如果需要认证
            }
        };

        // 全局数据
        let sampleQAData = [];
        let currentCaseId = null;

        // API请求函数
        async function fetchCaseData(caseId) {
            console.log('fetchCaseData', caseId);
            try {
                showLoading('正在加载案例数据...');

                const url = API_CONFIG.baseUrl;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        ...API_CONFIG.headers,
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type'
                    },
                    mode: 'cors', // 明确指定CORS模式
                    body: JSON.stringify({
                        articleId: "",
                        encryptionId: caseId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // 假设API返回的数据格式为：
                // {
                //   "caseId": "demo-001",
                //   "title": "案例标题",
                //   "appName": "NovaX Base",
                //   "appIcon": "icon-url",
                //   "conversations": [
                //     {
                //       "query": "用户问题",
                //       "answer": "AI回答"
                //     }
                //   ]
                // }

                if (data.conversations && Array.isArray(data.conversations)) {
                    sampleQAData = data.conversations;

                    // 更新页面信息
                    if (data.appName) {
                        document.getElementById('mobile-app-name').textContent = data.appName;
                        document.getElementById('desktop-app-name').textContent = data.appName;
                    }

                    if (data.appIcon) {
                        document.getElementById('mobile-app-icon').src = data.appIcon;
                        document.getElementById('desktop-app-icon').src = data.appIcon;
                    }

                    if (data.title) {
                        document.getElementById('case-title').textContent = data.title;
                    }

                    if (data.caseId) {
                        document.getElementById('case-id').textContent = data.caseId;
                    }

                    hideLoading();
                    return true;
                } else {
                    throw new Error('Invalid data format: conversations not found');
                }

            } catch (error) {
                console.error('获取案例数据失败:', error);
                hideLoading();
                showError('加载案例数据失败: ' + error.message);

                // 使用默认数据作为后备
                useDefaultData();
                return false;
            }
        }

        // 使用默认数据
        function useDefaultData() {
            sampleQAData = [
                {
                    query: "什么是人工智能？",
                    answer: "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。\n\n## 主要特点\n\n1. **学习能力** - 能够从数据中学习和改进\n2. **推理能力** - 能够基于已知信息得出结论\n3. **感知能力** - 能够理解和处理感官输入\n4. **语言处理** - 能够理解和生成自然语言\n\n人工智能的应用领域非常广泛，包括：\n- 机器学习\n- 自然语言处理\n- 计算机视觉\n- 机器人技术"
                },
                {
                    query: "AI在医疗领域有哪些应用？",
                    answer: "AI在医疗领域有着广泛而深入的应用，正在革命性地改变医疗行业：\n\n## 诊断辅助\n- **医学影像分析**：CT、MRI、X光片的智能识别\n- **病理诊断**：组织切片的自动分析\n- **皮肤病诊断**：通过图像识别皮肤疾病\n\n## 药物研发\n- **分子设计**：AI辅助新药分子结构设计\n- **临床试验优化**：提高试验效率和成功率\n- **副作用预测**：预测药物可能的不良反应\n\n## 个性化治疗\n- **基因分析**：基于基因信息制定治疗方案\n- **精准医疗**：为患者提供个性化治疗建议\n- **用药指导**：根据患者情况推荐最适合的药物\n\n这些应用大大提高了医疗效率和准确性。"
                }
            ];
        }

        // 显示加载状态
        function showLoading(message = '加载中...') {
            const container = document.getElementById('messages-container');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <span class="text-gray-600">${message}</span>
                    </div>
                </div>
            `;
        }

        // 隐藏加载状态
        function hideLoading() {
            // 加载状态会在开始演示时被清除
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('messages-container');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="text-red-800 font-medium">加载失败</p>
                                <p class="text-red-600 text-sm mt-1">${message}</p>
                                <button onclick="retryLoad()" class="mt-2 px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors">
                                    重试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 重试加载
        function retryLoad() {
            loadCaseData();
        }

        // 从URL参数获取案例ID
        function getCaseIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('caseId'); // 默认案例ID
        }

        // 工具函数
        function clearAllTimers() {
            timers.forEach(timer => clearTimeout(timer));
            timers = [];
        }

        function scrollToBottom() {
            const messagesEnd = document.getElementById('messages-end');
            messagesEnd.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }

        function goBack() {
            window.history.back();
        }

        // Markdown渲染函数
        function renderMarkdown(content) {
            if (typeof marked !== 'undefined') {
                return marked.parse(content);
            }
            // 简单的Markdown渲染后备方案
            return content
                .replace(/\n## (.*)/g, '<h2>$1</h2>')
                .replace(/\n# (.*)/g, '<h1>$1</h1>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n- (.*)/g, '<li>$1</li>')
                .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
                .replace(/\n/g, '<br>');
        }

        // 创建消息元素
        function createMessageElement(message, isInstant = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'mx-auto max-w-4xl relative group mb-6';
            messageDiv.id = message.id;

            if (message.type === 'user') {
                messageDiv.innerHTML = `
                    <div class="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
                        <div class="text-gray-800 text-base leading-relaxed">
                            ${renderMarkdown(message.content)}
                        </div>
                    </div>
                `;
            } else {
                const contentDiv = document.createElement('div');
                contentDiv.className = 'mr-auto max-w-4xl';
                
                const innerDiv = document.createElement('div');
                innerDiv.className = 'text-base text-gray-800 leading-relaxed prose prose-base max-w-none';
                innerDiv.id = `content-${message.id}`;
                
                if (isInstant || !message.isGenerating) {
                    innerDiv.innerHTML = renderMarkdown(message.content);
                } else {
                    // 开始打字机效果
                    startTypewriterEffect(innerDiv, message.content, message.id);
                }
                
                contentDiv.appendChild(innerDiv);
                messageDiv.appendChild(contentDiv);
            }

            return messageDiv;
        }

        // 打字机效果
        function startTypewriterEffect(element, content, messageId) {
            const renderedContent = renderMarkdown(content);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = renderedContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            let currentIndex = 0;
            element.innerHTML = '<span class="typewriter-cursor">|</span>';

            function typeNextChar() {
                if (currentIndex < textContent.length) {
                    const char = textContent[currentIndex];
                    const currentText = textContent.substring(0, currentIndex + 1);

                    // 重新渲染Markdown内容到当前位置
                    const partialContent = content.substring(0, Math.ceil((currentIndex + 1) * content.length / textContent.length));
                    element.innerHTML = renderMarkdown(partialContent) + '<span class="typewriter-cursor">|</span>';

                    currentIndex++;

                    const delay = char === ' ' ? 30 : (Math.random() * 50 + 20);
                    const timer = setTimeout(typeNextChar, delay);
                    timers.push(timer);
                } else {
                    // 完成打字，移除光标
                    element.innerHTML = renderedContent;

                    // 标记消息完成
                    const messageIndex = allMessages.findIndex(msg => msg.id === messageId);
                    if (messageIndex !== -1) {
                        allMessages[messageIndex].isGenerating = false;
                    }

                    // 继续下一条消息
                    const timer = setTimeout(() => {
                        currentMessageIndex++;
                        showNextMessage();
                    }, 1000);
                    timers.push(timer);
                }
            }

            typeNextChar();
        }

        // 显示下一条消息
        function showNextMessage() {
            if (currentMessageIndex >= allMessages.length) {
                isProcessing = false;
                return;
            }

            const message = allMessages[currentMessageIndex];
            const messageElement = createMessageElement(message, isInstantMode);

            document.getElementById('messages-container').appendChild(messageElement);
            scrollToBottom();

            if (message.type === 'user' || isInstantMode) {
                const timer = setTimeout(() => {
                    currentMessageIndex++;
                    showNextMessage();
                }, isInstantMode ? 100 : 1000);
                timers.push(timer);
            }
        }

        // 切换模式
        function toggleMode() {
            isInstantMode = !isInstantMode;

            const mobileBtn = document.getElementById('mobile-toggle-btn');
            const desktopBtn = document.getElementById('desktop-toggle-btn');

            if (isInstantMode) {
                mobileBtn.innerHTML = '<span>查看过程</span>';
                desktopBtn.innerHTML = '<span>查看过程</span>';
            } else {
                mobileBtn.innerHTML = '<span>查看结果</span>';
                desktopBtn.innerHTML = '<span>查看结果</span>';
            }

            // 重新开始演示（只有在有数据时才开始）
            if (sampleQAData && sampleQAData.length > 0) {
                startDemo();
            }
        }

        // 加载案例数据
        async function loadCaseData() {
            currentCaseId = getCaseIdFromUrl();
            const success = await fetchCaseData(currentCaseId);

            if (success || sampleQAData.length > 0) {
                startDemo();
            }
        }

        // 开始演示
        function startDemo() {
            if (isProcessing) return;

            // 检查是否有数据
            if (!sampleQAData || sampleQAData.length === 0) {
                showError('没有可显示的案例数据');
                return;
            }

            isProcessing = true;
            currentMessageIndex = 0;

            // 清除现有内容和定时器
            clearAllTimers();
            document.getElementById('messages-container').innerHTML = '';

            // 准备消息数据
            allMessages = [];
            sampleQAData.forEach((qa, index) => {
                allMessages.push({
                    id: `user-${index}`,
                    type: 'user',
                    content: qa.query
                });
                allMessages.push({
                    id: `assistant-${index}`,
                    type: 'assistant',
                    content: qa.answer,
                    isGenerating: !isInstantMode
                });
            });

            // 开始显示消息
            showNextMessage();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认应用信息
            const defaultAppName = 'NovaX Base';
            const defaultAppIcon = 'https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png';

            document.getElementById('mobile-app-name').textContent = defaultAppName;
            document.getElementById('desktop-app-name').textContent = defaultAppName;
            document.getElementById('mobile-app-icon').src = defaultAppIcon;
            document.getElementById('desktop-app-icon').src = defaultAppIcon;

            // 加载案例数据
            loadCaseData();
        });
    </script>
</body>
</html>
