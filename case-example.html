<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案例展示 - NovaX</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --bg-main: #f8fafc;
        }
        
        .typewriter-cursor {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .prose {
            max-width: none;
        }
        
        .prose h1, .prose h2, .prose h3 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        .prose p {
            margin-bottom: 1em;
        }
        
        .prose code {
            background-color: #f1f5f9;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
        }
        
        .prose pre {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .prose blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #64748b;
        }
        
        .prose ul, .prose ol {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }
        
        .prose li {
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app" class="relative h-screen">
        <!-- URL信息显示区域 (开发调试用) -->
        <div id="url-info" class="bg-yellow-100 border-b border-yellow-300 px-4 py-2 text-xs text-yellow-800">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center justify-between">
                    <div>
                        <strong>当前URL:</strong> <span id="current-url">加载中...</span>
                    </div>
                    <div>
                        <strong>案例ID:</strong> <span id="parsed-case-id" class="font-mono bg-yellow-200 px-2 py-1 rounded">加载中...</span>
                    </div>
                </div>
                <div class="mt-1 text-gray-600 flex items-center justify-between">
                    <div>
                        支持的URL格式: <code>/demo-001</code> | <code>/case/demo-001</code> | <code>?caseId=demo-001</code> | <code>#demo-001</code>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="testUrl('?caseId=test-001')" class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600">测试查询参数</button>
                        <button onclick="testUrl('#test-002')" class="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600">测试Hash</button>
                        <button onclick="toggleUrlInfo()" class="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600">隐藏</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="h-screen flex flex-col">
            <!-- 移动端头部 -->
            <div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
                <div class="flex items-center">
                    <button onclick="goBack()" class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <div class="flex items-center flex-1">
                        <div class="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3">
                            <img id="mobile-app-icon" src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" alt="AI" class="w-5 h-5 rounded-full">
                        </div>
                        <span id="mobile-app-name" class="text-lg font-semibold text-gray-800">NovaX Base</span>
                    </div>
                    <button onclick="toggleMode()" id="mobile-toggle-btn" class="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200">
                        <span>查看结果</span>
                    </button>
                </div>
            </div>

            <!-- 桌面端头部 -->
            <div class="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1">
                                    <img id="desktop-app-icon" src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" alt="AI" class="w-6 h-6 rounded-full">
                                </div>
                                <span id="desktop-app-name" class="text-xs text-gray-600 font-medium text-center block w-full">NovaX Base</span>
                            </div>
                            <div>
                                <h1 id="case-title" class="text-lg font-semibold text-gray-800">案例展示</h1>
                                <p class="text-sm">案例ID: <span id="case-id" class="text-blue-600 font-medium">demo-001</span></p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleDebug()" id="debug-btn" class="flex items-center justify-center px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white text-xs rounded-lg transition-colors duration-200">
                                <span>调试</span>
                            </button>
                            <button onclick="toggleMode()" id="desktop-toggle-btn" class="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md">
                                <span>查看结果</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调试面板 -->
            <div id="debug-panel" class="hidden fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-50">
                <div class="absolute top-4 right-4 bottom-4 w-96 bg-white rounded-lg shadow-xl overflow-hidden">
                    <div class="bg-gray-800 text-white p-3 flex items-center justify-between">
                        <h3 class="font-medium">调试信息</h3>
                        <button onclick="toggleDebug()" class="text-gray-300 hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="p-4 overflow-y-auto h-full">
                        <div id="debug-content" class="text-xs font-mono space-y-2">
                            <p>等待请求...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息列表区域 -->
            <div class="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6">
                <div class="max-w-4xl mx-auto" id="messages-container">
                    <!-- 消息将在这里动态生成 -->
                </div>
                <div id="messages-end"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isInstantMode = false;
        let isProcessing = false;
        let currentMessageIndex = 0;
        let allMessages = [];
        let timers = [];
        
        // API配置
        const API_CONFIG = {
            baseUrl: 'http://**************:48081/ai-base/index/qa-list?locale=en', // 替换为实际的API地址
            endpoints: {
                getCaseData: '/api/cases/{caseId}', // 获取案例数据
                getCaseList: '/api/cases' // 获取案例列表
            },
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': 'Bearer your-token-here' // 如果需要认证
            }
        };

        // 全局数据
        let sampleQAData = [];
        let currentCaseId = null;

        // API请求函数
        async function fetchCaseData(caseId) {
            console.log('fetchCaseData', caseId);
            try {
                showLoading('正在加载案例数据...');

                const url = API_CONFIG.baseUrl;

                // 模拟Postman的请求头
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                };

                const requestBody = {
                    articleId: "",
                    encryptionId: caseId
                };

                console.log('发送请求到:', url);
                console.log('请求头:', headers);
                console.log('请求体:', requestBody);

                // 添加调试日志
                addDebugLog(`发送POST请求到: ${url}`);
                addDebugLog(`请求头: ${JSON.stringify(headers, null, 2)}`);
                addDebugLog(`请求体: ${JSON.stringify(requestBody, null, 2)}`);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    mode: 'cors',
                    credentials: 'omit', // 不发送cookies
                    body: JSON.stringify(requestBody)
                });

                console.log('响应状态:', response.status, response.statusText);
                console.log('响应头:', [...response.headers.entries()]);

                // 添加响应调试日志
                addDebugLog(`响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                addDebugLog(`响应头: ${JSON.stringify([...response.headers.entries()], null, 2)}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('错误响应内容:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const responseText = await response.text();
                console.log('原始响应内容:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('解析后的数据:', data);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    throw new Error('响应不是有效的JSON格式: ' + responseText.substring(0, 200));
                }

                // 假设API返回的数据格式为：
                // {
                //   "caseId": "demo-001",
                //   "title": "案例标题",
                //   "appName": "NovaX Base",
                //   "appIcon": "icon-url",
                //   "conversations": [
                //     {
                //       "query": "用户问题",
                //       "answer": "AI回答"
                //     }
                //   ]
                // }

                if (data.conversations && Array.isArray(data.conversations)) {
                    sampleQAData = data.conversations;

                    // 更新页面信息
                    if (data.appName) {
                        document.getElementById('mobile-app-name').textContent = data.appName;
                        document.getElementById('desktop-app-name').textContent = data.appName;
                    }

                    if (data.appIcon) {
                        document.getElementById('mobile-app-icon').src = data.appIcon;
                        document.getElementById('desktop-app-icon').src = data.appIcon;
                    }

                    if (data.title) {
                        document.getElementById('case-title').textContent = data.title;
                    }

                    if (data.caseId) {
                        document.getElementById('case-id').textContent = data.caseId;
                    }

                    hideLoading();
                    return true;
                } else {
                    throw new Error('Invalid data format: conversations not found');
                }

            } catch (error) {
                console.error('获取案例数据失败:', error);
                addDebugLog(`请求失败: ${error.message}`, 'error');
                hideLoading();

                let errorMessage = '加载案例数据失败: ' + error.message;

                // 检查是否是CORS错误
                if (error.message.includes('CORS') || error.message.includes('Access-Control-Allow-Origin') || error.message.includes('fetch')) {
                    errorMessage = `
                        <div class="space-y-3">
                            <p class="font-medium text-red-800">网络请求失败</p>
                            <div class="text-sm text-red-600 space-y-2">
                                <p><strong>可能的原因：</strong></p>
                                <ul class="list-disc list-inside space-y-1 ml-2">
                                    <li>CORS跨域限制</li>
                                    <li>网络连接问题</li>
                                    <li>服务器配置问题</li>
                                </ul>
                                <p class="mt-3"><strong>解决方案：</strong></p>
                                <ol class="list-decimal list-inside space-y-1 ml-2">
                                    <li>使用HTTP服务器访问页面</li>
                                    <li>检查网络连接</li>
                                    <li>联系后端开发者配置CORS</li>
                                </ol>
                            </div>
                            <div class="mt-3 p-3 bg-blue-50 rounded text-xs">
                                <p class="font-medium text-blue-800">启动本地服务器：</p>
                                <code class="block mt-1 p-1 bg-blue-100 rounded">python -m http.server 8000</code>
                                <p class="mt-1 text-blue-700">然后访问: http://localhost:8000/case-example.html</p>
                            </div>
                        </div>
                    `;
                }

                showError(errorMessage);

                // 使用默认数据作为后备
                useDefaultData();
                return false;
            }
        }

        // 使用默认数据
        function useDefaultData() {
            sampleQAData = [
                {
                    query: "什么是人工智能？",
                    answer: "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。\n\n## 主要特点\n\n1. **学习能力** - 能够从数据中学习和改进\n2. **推理能力** - 能够基于已知信息得出结论\n3. **感知能力** - 能够理解和处理感官输入\n4. **语言处理** - 能够理解和生成自然语言\n\n人工智能的应用领域非常广泛，包括：\n- 机器学习\n- 自然语言处理\n- 计算机视觉\n- 机器人技术"
                },
                {
                    query: "AI在医疗领域有哪些应用？",
                    answer: "AI在医疗领域有着广泛而深入的应用，正在革命性地改变医疗行业：\n\n## 诊断辅助\n- **医学影像分析**：CT、MRI、X光片的智能识别\n- **病理诊断**：组织切片的自动分析\n- **皮肤病诊断**：通过图像识别皮肤疾病\n\n## 药物研发\n- **分子设计**：AI辅助新药分子结构设计\n- **临床试验优化**：提高试验效率和成功率\n- **副作用预测**：预测药物可能的不良反应\n\n## 个性化治疗\n- **基因分析**：基于基因信息制定治疗方案\n- **精准医疗**：为患者提供个性化治疗建议\n- **用药指导**：根据患者情况推荐最适合的药物\n\n这些应用大大提高了医疗效率和准确性。"
                }
            ];
        }

        // 显示加载状态
        function showLoading(message = '加载中...') {
            const container = document.getElementById('messages-container');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <span class="text-gray-600">${message}</span>
                    </div>
                </div>
            `;
        }

        // 隐藏加载状态
        function hideLoading() {
            // 加载状态会在开始演示时被清除
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('messages-container');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="text-red-800 font-medium">加载失败</p>
                                <p class="text-red-600 text-sm mt-1">${message}</p>
                                <button onclick="retryLoad()" class="mt-2 px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors">
                                    重试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 重试加载
        function retryLoad() {
            loadCaseData();
        }

        // 从URL路径获取案例ID
        function getCaseIdFromUrl() {
            const fullUrl = window.location.href;
            const pathname = window.location.pathname;
            const search = window.location.search;
            const hash = window.location.hash;

            // 添加调试日志
            addDebugLog(`完整URL: ${fullUrl}`, 'info');
            addDebugLog(`路径: ${pathname}`, 'info');
            addDebugLog(`查询参数: ${search}`, 'info');
            addDebugLog(`Hash: ${hash}`, 'info');

            console.log('URL解析信息:');
            console.log('- 完整URL:', fullUrl);
            console.log('- 路径:', pathname);
            console.log('- 查询参数:', search);
            console.log('- Hash:', hash);

            // 移除开头的斜杠并分割路径
            const pathParts = pathname.replace(/^\/+/, '').split('/').filter(part => part);
            console.log('- 路径部分:', pathParts);
            addDebugLog(`路径部分: [${pathParts.join(', ')}]`, 'info');

            // 方法1: 从路径获取 (例如: /demo-001 或 /case/demo-001)

            // 如果路径是 /demo-001 格式 (单个路径段，且不是HTML文件)
            if (pathParts.length === 1 && pathParts[0] && !pathParts[0].endsWith('.html')) {
                const caseId = pathParts[0];
                console.log('从根路径获取caseId:', caseId);
                addDebugLog(`从根路径获取caseId: ${caseId}`, 'success');
                return caseId;
            }

            // 如果路径是 /case/demo-001 格式
            if (pathParts.length >= 2 && pathParts[0] === 'case') {
                const caseId = pathParts[1];
                console.log('从case路径获取caseId:', caseId);
                addDebugLog(`从case路径获取caseId: ${caseId}`, 'success');
                return caseId;
            }

            // 方法2: 从URL查询参数获取 (兼容旧方式: ?caseId=xxx)
            const urlParams = new URLSearchParams(search);
            const queryParamCaseId = urlParams.get('caseId');
            if (queryParamCaseId) {
                console.log('从查询参数获取caseId:', queryParamCaseId);
                addDebugLog(`从查询参数获取caseId: ${queryParamCaseId}`, 'success');
                return queryParamCaseId;
            }

            // 方法3: 从hash获取 (例如: #demo-001)
            const hashValue = hash.replace('#', '');
            if (hashValue) {
                console.log('从hash获取caseId:', hashValue);
                addDebugLog(`从hash获取caseId: ${hashValue}`, 'success');
                return hashValue;
            }

            // 默认值
            console.log('使用默认caseId: demo-001');
            addDebugLog('未找到caseId，使用默认值: demo-001', 'info');
            return 'demo-001';
        }

        // URL测试函数
        function testUrl(urlSuffix) {
            const baseUrl = window.location.origin + window.location.pathname;
            const newUrl = baseUrl + urlSuffix;
            console.log('测试URL:', newUrl);
            window.location.href = newUrl;
        }

        function toggleUrlInfo() {
            const urlInfo = document.getElementById('url-info');
            urlInfo.classList.toggle('hidden');
        }

        // 调试相关函数
        function toggleDebug() {
            const panel = document.getElementById('debug-panel');
            panel.classList.toggle('hidden');
        }

        function addDebugLog(message, type = 'info') {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-gray-700';

            const logEntry = document.createElement('div');
            logEntry.className = `p-2 border-l-2 ${type === 'error' ? 'border-red-500 bg-red-50' : type === 'success' ? 'border-green-500 bg-green-50' : 'border-blue-500 bg-blue-50'}`;
            logEntry.innerHTML = `
                <div class="text-xs text-gray-500">${timestamp}</div>
                <div class="${colorClass}">${message}</div>
            `;

            debugContent.appendChild(logEntry);
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        function clearDebugLog() {
            document.getElementById('debug-content').innerHTML = '<p>调试日志已清空</p>';
        }

        // 工具函数
        function clearAllTimers() {
            timers.forEach(timer => clearTimeout(timer));
            timers = [];
        }

        function scrollToBottom() {
            const messagesEnd = document.getElementById('messages-end');
            messagesEnd.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }

        function goBack() {
            window.history.back();
        }

        // Markdown渲染函数
        function renderMarkdown(content) {
            if (typeof marked !== 'undefined') {
                return marked.parse(content);
            }
            // 简单的Markdown渲染后备方案
            return content
                .replace(/\n## (.*)/g, '<h2>$1</h2>')
                .replace(/\n# (.*)/g, '<h1>$1</h1>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n- (.*)/g, '<li>$1</li>')
                .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
                .replace(/\n/g, '<br>');
        }

        // 创建消息元素
        function createMessageElement(message, isInstant = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'mx-auto max-w-4xl relative group mb-6';
            messageDiv.id = message.id;

            if (message.type === 'user') {
                messageDiv.innerHTML = `
                    <div class="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
                        <div class="text-gray-800 text-base leading-relaxed">
                            ${renderMarkdown(message.content)}
                        </div>
                    </div>
                `;
            } else {
                const contentDiv = document.createElement('div');
                contentDiv.className = 'mr-auto max-w-4xl';
                
                const innerDiv = document.createElement('div');
                innerDiv.className = 'text-base text-gray-800 leading-relaxed prose prose-base max-w-none';
                innerDiv.id = `content-${message.id}`;
                
                if (isInstant || !message.isGenerating) {
                    innerDiv.innerHTML = renderMarkdown(message.content);
                } else {
                    // 开始打字机效果
                    startTypewriterEffect(innerDiv, message.content, message.id);
                }
                
                contentDiv.appendChild(innerDiv);
                messageDiv.appendChild(contentDiv);
            }

            return messageDiv;
        }

        // 打字机效果
        function startTypewriterEffect(element, content, messageId) {
            const renderedContent = renderMarkdown(content);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = renderedContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            let currentIndex = 0;
            element.innerHTML = '<span class="typewriter-cursor">|</span>';

            function typeNextChar() {
                if (currentIndex < textContent.length) {
                    const char = textContent[currentIndex];
                    const currentText = textContent.substring(0, currentIndex + 1);

                    // 重新渲染Markdown内容到当前位置
                    const partialContent = content.substring(0, Math.ceil((currentIndex + 1) * content.length / textContent.length));
                    element.innerHTML = renderMarkdown(partialContent) + '<span class="typewriter-cursor">|</span>';

                    currentIndex++;

                    const delay = char === ' ' ? 30 : (Math.random() * 50 + 20);
                    const timer = setTimeout(typeNextChar, delay);
                    timers.push(timer);
                } else {
                    // 完成打字，移除光标
                    element.innerHTML = renderedContent;

                    // 标记消息完成
                    const messageIndex = allMessages.findIndex(msg => msg.id === messageId);
                    if (messageIndex !== -1) {
                        allMessages[messageIndex].isGenerating = false;
                    }

                    // 继续下一条消息
                    const timer = setTimeout(() => {
                        currentMessageIndex++;
                        showNextMessage();
                    }, 1000);
                    timers.push(timer);
                }
            }

            typeNextChar();
        }

        // 显示下一条消息
        function showNextMessage() {
            if (currentMessageIndex >= allMessages.length) {
                isProcessing = false;
                return;
            }

            const message = allMessages[currentMessageIndex];
            const messageElement = createMessageElement(message, isInstantMode);

            document.getElementById('messages-container').appendChild(messageElement);
            scrollToBottom();

            if (message.type === 'user' || isInstantMode) {
                const timer = setTimeout(() => {
                    currentMessageIndex++;
                    showNextMessage();
                }, isInstantMode ? 100 : 1000);
                timers.push(timer);
            }
        }

        // 切换模式
        function toggleMode() {
            isInstantMode = !isInstantMode;

            const mobileBtn = document.getElementById('mobile-toggle-btn');
            const desktopBtn = document.getElementById('desktop-toggle-btn');

            if (isInstantMode) {
                mobileBtn.innerHTML = '<span>查看过程</span>';
                desktopBtn.innerHTML = '<span>查看过程</span>';
            } else {
                mobileBtn.innerHTML = '<span>查看结果</span>';
                desktopBtn.innerHTML = '<span>查看结果</span>';
            }

            // 重新开始演示（只有在有数据时才开始）
            if (sampleQAData && sampleQAData.length > 0) {
                startDemo();
            }
        }

        // 加载案例数据
        async function loadCaseData() {
            // 更新URL信息显示
            document.getElementById('current-url').textContent = window.location.href;

            currentCaseId = getCaseIdFromUrl();

            // 立即显示获取到的caseId
            console.log('获取到的caseId:', currentCaseId);
            document.getElementById('case-id').textContent = currentCaseId || 'demo-001';
            document.getElementById('parsed-case-id').textContent = currentCaseId || 'demo-001';

            // 更新页面标题
            if (currentCaseId) {
                document.title = `案例展示 - ${currentCaseId} - NovaX`;
            }

            const success = await fetchCaseData(currentCaseId);

            if (success || sampleQAData.length > 0) {
                startDemo();
            }
        }

        // 开始演示
        function startDemo() {
            if (isProcessing) return;

            // 检查是否有数据
            if (!sampleQAData || sampleQAData.length === 0) {
                showError('没有可显示的案例数据');
                return;
            }

            isProcessing = true;
            currentMessageIndex = 0;

            // 清除现有内容和定时器
            clearAllTimers();
            document.getElementById('messages-container').innerHTML = '';

            // 准备消息数据
            allMessages = [];
            sampleQAData.forEach((qa, index) => {
                allMessages.push({
                    id: `user-${index}`,
                    type: 'user',
                    content: qa.query
                });
                allMessages.push({
                    id: `assistant-${index}`,
                    type: 'assistant',
                    content: qa.answer,
                    isGenerating: !isInstantMode
                });
            });

            // 开始显示消息
            showNextMessage();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认应用信息
            const defaultAppName = 'NovaX Base';
            const defaultAppIcon = 'https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png';

            document.getElementById('mobile-app-name').textContent = defaultAppName;
            document.getElementById('desktop-app-name').textContent = defaultAppName;
            document.getElementById('mobile-app-icon').src = defaultAppIcon;
            document.getElementById('desktop-app-icon').src = defaultAppIcon;

            // 加载案例数据
            loadCaseData();
        });
    </script>
</body>
</html>
