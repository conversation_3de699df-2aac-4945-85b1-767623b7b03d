import { useState, useMemo, useEffect, useRef, useCallback } from 'react'
import { XAiApi, IGetAiAppInfoResponse, PackageByKey } from '../api/src/xai-api';
import Cookies from 'js-cookie';

import { useSimpleTranslation,useI18nRouter } from '../i18n/simple-hooks';
import { useLoginStatus } from '../hooks/useLoginStatus';
import LanguageSwitcher, { LanguageSwitcherIcon } from './LanguageSwitcher';
import { LogoutIcon, VIPIcon, DiamondCrownIcon } from './icons/Icons';
import { message } from 'antd';

interface HeaderProps {
  onMenuClick?: () => void
  currentApp: IGetAiAppInfoResponse
  onAppSelect: (appUuid: string) => void
  appList: IGetAiAppInfoResponse[]
  xAiApi: XAiApi
  subStatusDetail: PackageByKey | null
  showSubscriptionModal: boolean
  setShowSubscriptionModal: (show: boolean) => void
  appListSub: Map<string, any>
  fetchAllAppSubscriptions: () => Promise<void>
  setAppList: (appList: IGetAiAppInfoResponse[] | ((prev: IGetAiAppInfoResponse[]) => IGetAiAppInfoResponse[])) => void
}

// 用户菜单项将在组件内部使用翻译函数动态生成

const Header = ({
  onMenuClick,
  currentApp,
  onAppSelect,
  appList,
  xAiApi,
  subStatusDetail,
  showSubscriptionModal,
  setShowSubscriptionModal,
  appListSub,
  fetchAllAppSubscriptions,
  setAppList
}: HeaderProps) => {
  const { t } = useSimpleTranslation()
  const defAvatar = 'https://img.medsci.cn/web/img/user_icon.png'

  // 辅助函数：获取当前应用的订阅等级
  const getCurrentAppTier = (): 'base' | 'pro' | 'ultra' | null => {
    if (!currentApp || !subStatusDetail) return null

    const subStatus = subStatusDetail.subStatus
    if (subStatus !== 1 && subStatus !== 3) return null // 未订阅或已过期

    if (currentApp.appNameEn.includes('base')) return 'base'
    if (currentApp.appNameEn.includes('pro')) return 'pro'
    if (currentApp.appNameEn.includes('ultra')) return 'ultra'

    return null
  }

  // 辅助函数：根据等级获取订阅状态图标
  const getSubscriptionIcon = (tier: 'base' | 'pro' | 'ultra' | null, size: number = 18) => {
    if (!tier) return null

    const iconProps = { width: size, height: size }

    switch (tier) {
      case 'base':
        return null // Base版本不显示任何图标
      case 'pro':
        return <VIPIcon {...iconProps} />
      case 'ultra':
        return <DiamondCrownIcon {...iconProps} />
      default:
        return null
    }
  }
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showVersionMenu, setShowVersionMenu] = useState(false) // 新增状态: 控制版本菜单显示
  const { userInfo, isLoggedIn, clearUserInfo } = useLoginStatus()
  const [avatar, setAvatar] = useState(userInfo?.avatar || defAvatar)
  const [username, setUsername] = useState(userInfo?.userName || '')
  const [avatarError, setAvatarError] = useState(false) // 添加头像加载错误状态
  const [avatarLoaded, setAvatarLoaded] = useState(false) // 添加头像加载完成状态
  const [realAvatarUrl, setRealAvatarUrl] = useState<string | null>(null) // 真实头像URL
  const { currentLanguage, changeLanguage } = useI18nRouter()
  const [paymentLoading, setPaymentLoading] = useState(false)

  // 添加ref引用用于点击外部关闭
  const userMenuRef = useRef<HTMLDivElement>(null)
  const versionMenuRef = useRef<HTMLDivElement>(null)
  

  // 动态生成用户菜单项
  const userMenuItems = useMemo(() => [
    // { icon: '⚙️', label: t('navigation.settings'), action: 'settings' },
    // { icon: '💬', label: t('navigation.feedback'), action: 'feedback' },
    // { icon: '📊', label: t('navigation.export'), action: 'export' },
    {
      icon: <LogoutIcon width={16} height={16} className="text-red-500" />,
      label: t('auth.logoutButton'),
      action: 'logout',
      danger: true
    }
  ], [t])

  // 头像预加载函数
  const preloadAvatar = useCallback((avatarUrl: string) => {
    // 如果没有URL、URL为空或者就是默认头像，不需要预加载
    if (!avatarUrl || avatarUrl.trim() === '' || avatarUrl === defAvatar) {
      console.log('跳过头像预加载，使用默认头像')
      setRealAvatarUrl(null)
      setAvatarLoaded(false)
      setAvatarError(false)
      return Promise.resolve()
    }

    console.log('开始预加载用户头像:', avatarUrl)
    return new Promise<void>((resolve, reject) => {
      const img = new Image()

      // 缩短超时时间，避免长时间显示加载动画
      const timeout = setTimeout(() => {
        console.log('头像预加载超时，使用默认头像:', avatarUrl)
        setAvatarError(true)
        setAvatarLoaded(false)
        setRealAvatarUrl(null)
        reject(new Error('Avatar load timeout'))
      }, 3000) // 缩短到3秒超时

      img.onload = () => {
        clearTimeout(timeout)
        console.log('头像预加载成功:', avatarUrl)
        setRealAvatarUrl(avatarUrl)
        setAvatarLoaded(true)
        setAvatarError(false)
        resolve()
      }

      img.onerror = () => {
        clearTimeout(timeout)
        console.log('头像预加载失败，使用默认头像:', avatarUrl)
        setAvatarError(true)
        setAvatarLoaded(false)
        setRealAvatarUrl(null)
        reject(new Error('Avatar load failed'))
      }

      img.src = avatarUrl
    })
  }, [defAvatar])

  // 监听用户信息变化，立即更新用户名并预加载头像
  useEffect(() => {
    if (userInfo) {
      const newUsername = userInfo.userName || ''

      console.log('用户信息变化:', {
        userName: newUsername,
        avatar: userInfo.avatar,
        hasAvatar: !!userInfo.avatar,
        avatarLength: userInfo.avatar?.length || 0
      })

      // 立即更新用户名
      setUsername(prevUsername => {
        if (prevUsername !== newUsername) {
          console.log('立即更新用户名:', newUsername)
          return newUsername
        }
        return prevUsername
      })

      // 处理头像逻辑
      if (userInfo.avatar && userInfo.avatar.trim() !== '') {
        // 如果有新的头像URL且与当前不同，进行预加载
        if (userInfo.avatar !== realAvatarUrl) {
          console.log('检测到新头像，开始预加载:', userInfo.avatar)
          setAvatarLoaded(false)
          setAvatarError(false)
          setRealAvatarUrl(null)

          // 异步预加载真实头像，设置较短的超时时间
          preloadAvatar(userInfo.avatar).catch((error) => {
            console.log('头像预加载失败，将使用默认头像:', error.message)
            // 确保失败时状态正确设置
            setAvatarError(true)
            setAvatarLoaded(false)
            setRealAvatarUrl(null)
          })
        } else if (realAvatarUrl && avatarLoaded) {
          // 如果头像URL相同且已加载，保持当前状态
          console.log('头像已加载，保持当前状态')
        }
      } else {
        // 用户没有设置头像或头像为空，清除相关状态
        console.log('用户未设置头像或头像为空，使用默认头像')
        setAvatarLoaded(false)
        setAvatarError(false)
        setRealAvatarUrl(null)
      }
    } else {
      setUsername('')
      setAvatarError(false)
      setAvatarLoaded(false)
      setRealAvatarUrl(null)
    }
  }, [userInfo, preloadAvatar, realAvatarUrl])

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查用户菜单
      if (showUserMenu && userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }

      // 检查版本菜单
      if (showVersionMenu && versionMenuRef.current && !versionMenuRef.current.contains(event.target as Node)) {
        setShowVersionMenu(false)
      }
    }

    if (showUserMenu || showVersionMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu, showVersionMenu])

  const login = async() => {
    await xAiApi.logout()
    window.sessionStorage.setItem('redirectUrl', window.location.href)
		if (!currentLanguage ||  currentLanguage === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  (window as any).addLoginDom?.()
		} else {
		  (window as any).top.location.href =  location.origin + '/' + currentLanguage + "/login"
		}
	}

		const language = () => {
			return Cookies.get('ai_apps_lang') ? Cookies.get('ai_apps_lang') : navigator.language
		}

	const logout = async () => {
		// 清除本地状态
		clearUserInfo()
		setUsername('')
		setAvatarError(false)

		await xAiApi.logout()

		// 跳转到登出页面
		if (window.location.origin.includes("medsci.cn")) {
		  (window as any).top.location.href = "https://www.medsci.cn/sso_logout?redirectUrl=" + (location.pathname.split("/").length==4 ?location.origin + location.pathname.substring(0,location.pathname.lastIndexOf('/')) : (window as any).top.location.href);
		} else {
		  (window as any).top.location.href = `https://portal-test.medon.com.cn/sso_logout?redirectUrl=` + (location.pathname.split("/").length==4 ? location.origin + location.pathname.substring(0,location.pathname.lastIndexOf('/')) : (window as any).top.location.href);
		}
	};

  // 计算最终显示的头像URL - 优先用户头像，渐进式加载
  const displayAvatar = useMemo(() => {
    // 如果没有登录，显示默认头像
    if (!userInfo) {
      return defAvatar
    }

    // 如果用户没有设置头像，直接显示默认头像
    if (!userInfo.avatar) {
      return defAvatar
    }

    // 如果头像加载出错，显示默认头像
    if (avatarError) {
      return defAvatar
    }

    // 如果真实头像已加载完成，显示真实头像
    if (avatarLoaded && realAvatarUrl) {
      return realAvatarUrl
    }

    // 预加载期间显示默认头像，避免显示可能无效的URL
    return defAvatar
  }, [userInfo, avatarError, avatarLoaded, realAvatarUrl, defAvatar])

  const errorImg = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement

    // 避免重复处理同一个错误，如果已经是默认头像就不再处理
    if (avatarError || target.src === defAvatar || target.src.includes('user_icon.png')) {
      return
    }

    console.log('头像显示失败，切换到默认头像:', target.src)
    setAvatarError(true)
    setAvatarLoaded(false)
    setRealAvatarUrl(null)

    // 立即设置为默认头像
    target.src = defAvatar
  }

  const handleAllHide = () => {
    setShowUserMenu(false)
    setShowVersionMenu(false)
    setShowSubscriptionModal(false)
  }

  const handleVersionMenuToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    // 关闭其他菜单，但保持当前菜单的切换逻辑
    setShowUserMenu(false)
    setShowSubscriptionModal(false)
    setShowVersionMenu(prev => !prev)
  }

  const handleUserMenuToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    // 关闭其他菜单，但保持当前菜单的切换逻辑
    setShowVersionMenu(false)
    setShowSubscriptionModal(false)
    setShowUserMenu(prev => !prev)
  }

  const handleUserMenuClick = (action: string) => {
    if (action === 'logout') {
      logout()
    }
    
    setShowUserMenu(false)
  }

  // 免费版到Pro版的映射关系
  const getProVersionMapping = (appNameEn: string): string | null => {
    const mappings: Record<string, string> = {
      'novax-base': 'novax-pro',
      'elavax-base': 'elavax-pro',
      // 可以根据需要添加更多映射
    }
    return mappings[appNameEn] || null
  }

  // 检查是否为base应用
  const isBaseApp = (appNameEn: string): boolean => {
    return appNameEn.includes('base')
  }

  // 支付方法
  const subscribe = async (feeType: any) => {
    const token = Cookies.get('yudaoToken');
    if (!token) {
      console.log('=====', '用户未登录')
      return
    }
    if (!currentApp || !feeType) {
      console.log('=====', '当前应用信息不存在')
      return
    }
    if (paymentLoading) {
      console.log('===支付中')
      return
    }
    const feeSub = appListSub.get(feeType.packageKey)
    if (feeSub?.subStatus === 1 || feeSub?.subStatus === 3) {
        console.log('===已订阅', feeSub)
        return
    }
    const sub = appListSub.get(currentApp.appNameEn)
    if (feeType.type == "免费") {
      if (sub?.subStatus === 1) {
        console.log('===已订阅', currentApp)
        return
      }
    } else {
      if ((sub?.subStatus === 1 && sub?.packageType !== '免费') || sub?.subStatus === 3) {
        console.log('===已订阅', currentApp)
        return
      }
    }

    try {
      setPaymentLoading(true)
      const subscriptionParams = {
        appUuid: currentApp.appUuid || "",
        priceId: feeType.priceId,
        monthNum: feeType.monthNum,
        packageKey: feeType.packageKey,
        packageType: feeType.type
      };
      const payInfo = await xAiApi.createSubscription(subscriptionParams);
      if (feeType.type == "免费") {
        console.log('===自动订阅', payInfo);

        // 免费订阅成功后，更新订阅状态
        await fetchAllAppSubscriptions();

        // 重新计算currentApp - 获取最新的应用信息
        try {
          const updatedAppInfo = await xAiApi.getAppByUuid({ appUuid: currentApp.appUuid });
          setAppList(prevAppList =>
            prevAppList.map(app =>
              app.appUuid === currentApp.appUuid ? updatedAppInfo : app
            )
          );

        } catch (error) {
          console.error('重新获取当前应用信息失败:', error);
        }

        // 短暂延时避免重复订阅
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(t('subscription.paymentFailed'), error)
    } finally {
      setPaymentLoading(false)
    }
  }

  // 版本切换处理
  const handleVersionChange = (appUuid: string, appNameEn: string) => {
    if (!isBaseApp(appNameEn)) {
      message.info(t('chat.comingSoon'))
      console.log('暂时不能对外开放')
      return
    }
    if (onAppSelect) {
      onAppSelect(appUuid)
    }
    console.log('切换版本到:', appUuid)
    setShowVersionMenu(false)
  }

  // 处理订阅按钮点击
  const handleSubscriptionClick = async () => {
    if (!userInfo) {
      login()
      return
    }

    // 如果是base应用且未订阅免费版，先自动订阅免费版再跳转到pro版
    if (isBaseApp(currentApp.appNameEn)) {
      const subStatusDetail = appListSub.get(currentApp.appNameEn)
      
      // 订阅成功后，切换到对应的pro版本
      if (!subStatusDetail || subStatusDetail.subStatus === 0 || subStatusDetail.subStatus === 2) {
        // 如果未订阅或已过期，先自动订阅免费版
        // 获取免费套餐
        const freePackage = subStatusDetail?.feeTypes?.find((fee: any) => fee.type === '免费' || !fee.feePrice)
        if (freePackage) {
          console.log('检测到base应用未订阅，先自动订阅免费版')
          try {
            await subscribe(freePackage)
            console.log('免费版订阅成功，切换到pro版本:')
            toPro()
            return
          } catch (error) {
            console.error('自动订阅免费版失败:', error)
          }
        }
      }
      if (subStatusDetail.packageType === '免费') {
        console.log('已订阅免费版，切换到pro版本')
        toPro()
        return
      }
      if (subStatusDetail.subStatus === 1) {
        console.log('已订阅base免费版，切换到pro版本')
        toPro()
        return
      }
    }

    // 其他情况，正常显示订阅弹窗
    setShowSubscriptionModal(!showSubscriptionModal)
  }

  const toPro = () => { 
    const proAppName = getProVersionMapping(currentApp.appNameEn)
    if (proAppName) {
      onAppSelect(proAppName)
      // 延迟显示pro版本的订阅弹窗
      setTimeout(() => {
        setShowSubscriptionModal(true)
      }, 500)
    }
  }

  // 处理订阅弹窗关闭
  const handleSubscriptionClose = () => {
    setShowSubscriptionModal(false)
    // 确保按钮失去焦点，防止保持黑色边框
    setTimeout(() => {
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && activeElement.blur) {
        activeElement.blur()
      }
    }, 100)
  }

  // 处理订阅套餐选择
  const handleSubscribe = async (planId: string) => {
    console.log('选择订阅套餐:', planId)
    // 这里可以添加实际的订阅逻辑
    // 例如调用支付接口等
    try {
      // 示例：调用订阅API
      // await xAiApi.subscribe({ planId })
      alert(`已选择订阅套餐: ${planId}`)
    } catch (error) {
      console.error('订阅失败:', error)
      alert('订阅失败，请重试')
    }
  }

  return (
    <>
      <header className="top-0 left-0 right-0 z-50 h-16 border-gray-100 backdrop-blur-md bg-opacity-95 flex items-center justify-between px-4 md:px-8" style={{ backgroundColor: 'var(--bg-main)' }}>
        {/* 左侧Logo区域 */}
        <div className="flex items-center gap-2 md:gap-3">
          {/* 移动端菜单按钮 */}
          {onMenuClick && (
            <button
              onClick={onMenuClick}
              className="h-[40px] w-[30px] flex items-center justify-center text-gray-600 hover:text-gray-900 rounded-lg transition-colors md:hidden"
            >
              <span className="text-sm">☰</span>
            </button>
          )}
          
          {/* 应用切换下拉菜单 - 优化设计 */}
          <div ref={versionMenuRef} className="relative">
            <button
              onClick={handleVersionMenuToggle}
              className="
                group flex items-center space-x-2
                px-3 h-[40px]
                bg-white dark:bg-gray-800
                border border-gray-200 dark:border-gray-600
                rounded-xl shadow-sm
                hover:bg-gray-50 dark:hover:bg-gray-700
                hover:border-gray-300 dark:hover:border-gray-500
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                transition-all duration-200
              "
              aria-expanded={showVersionMenu}
              aria-haspopup="true"
            >
              {/* 应用图标 */}
              <span className="flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                <img src={currentApp?.appIcon} alt={currentApp?.appName} className="w-5 h-5" />
              </span>

              {/* 应用名称 */}
              <span className="hidden sm:inline font-medium text-sm text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200">
                {currentApp?.appName}
              </span>

              {/* 下拉箭头 - 与语言切换器保持一致 */}
              <svg
                className={`w-3.5 h-3.5 text-gray-400 group-hover:text-gray-600 transition-all duration-200 ${
                  showVersionMenu ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {/* 应用选择下拉菜单 - 优化设计 */}
            {showVersionMenu && (
              <div
                className="
                  absolute top-full mt-2
                  left-0
                  bg-white dark:bg-gray-800
                  border border-gray-200 dark:border-gray-600
                  rounded-xl shadow-xl
                  z-50 min-w-[220px]
                  overflow-hidden
                  backdrop-blur-sm
                  fade-in
                  ring-1 ring-black ring-opacity-5
                "
                role="menu"
                aria-orientation="vertical"
              >
                {appList.map((app, index) => {
                  const isSelected = currentApp.appUuid === app.appUuid

                  return (
                    <button
                      key={app.appUuid}
                      onClick={() => handleVersionChange(app.appUuid, app.appNameEn)}
                      className={`
                        w-full flex items-center space-x-3 px-4 py-3
                        text-left transition-all duration-200
                        group relative
                        ${!app.appNameEn.includes('base')
                          ? 'bg-gray-50 text-gray-400 cursor-not-allowed hover:bg-gray-50'
                          : isSelected
                            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }
                        ${index === 0 ? 'rounded-t-xl' : ''}
                        ${index === appList.length - 1 ? 'rounded-b-xl' : ''}
                      `}
                      role="menuitem"
                      aria-selected={isSelected}
                    >
                      {/* 应用图标 */}
                      <span className={`flex-shrink-0 transition-transform duration-200 ${
                        !app.appNameEn.includes('base')
                          ? 'opacity-50'
                          : 'group-hover:scale-110'
                      }`}>
                        <img src={app.appIcon} alt={app.appName} className="w-5 h-5" />
                      </span>

                      {/* 应用名称 */}
                      <span className="font-medium flex-1 text-sm">
                        {app.appName}
                      </span>

                      {/* 选中标记 - 与语言切换器保持一致 */}
                      {isSelected && (
                        <svg
                          className="w-4 h-4 text-blue-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  )
                })}
              </div>
            )}
          </div>
        </div>
        
        {/* 中间空白区域 */}
        <div className="flex-1"></div>

        {/* 右侧操作区域 - 统一间距布局 */}
        <div className="flex items-center flex-shrink-0 space-x-4 md:space-x-6">

          {/* 订阅按钮 - 只在有订阅状态详情时显示 */}
          {subStatusDetail && (
            <button
              onClick={handleSubscriptionClick}
              className="gradient-button points modern-button px-3 h-10 flex items-center rounded shadow-md hover:shadow-lg text-sm font-medium"
            >
              <span className="hidden sm:inline">
                {isBaseApp(currentApp.appNameEn)?t('subscription.upgrade'):
                (subStatusDetail?.subStatus==1
                  ? t('subscription.upgrade')
                  : t('subscription.subscribe'))
                }
              </span>
              <span><img className="h-6 max-h-6 w-auto object-contain" src="https://img.medsci.cn/202507/81b770ae34d745d29a9b53f0775107db-pWv2rVrLBAg8.png" alt="Subscribe" /></span>
            </button>
          )}

          {/* 语言切换器 */}
          <LanguageSwitcher
            className="h-[40px]"
            showText={false}
            showFlag={true}
            size="medium"
            dropdownPosition="right"
          />

          {/* 用户头像/菜单 */}
          <div ref={userMenuRef} className="relative flex justify-center">
            {!userInfo ? (
              // 未登录状态 - 显示登录按钮
              <button
                onClick={login}
                className="px-3 py-2 bg-gradient-to-r to-indigo-600 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105"
              >
                {t('auth.loginButton')}
              </button>
            ) : (
              // 已登录状态 - 显示用户头像
              <>
                <div className="relative">
                  <button
                    onClick={handleUserMenuToggle}
                    className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br rounded-full flex items-center justify-center text-white text-sm md:text-base font-bold hover:shadow-lg transition-all duration-200 hover:scale-105 relative"
                    title={username}
                  >
                      <img
                        src={displayAvatar}
                        alt={username}
                        onError={errorImg}
                        className={`w-full h-full object-cover rounded-full transition-opacity duration-300 ${
                          userInfo?.avatar && !avatarLoaded && !avatarError ? 'opacity-75' : 'opacity-100'
                        }`}
                      />
                      {/* 头像加载中的微妙指示 - 只在有有效头像URL且正在加载时显示 */}
                      {userInfo?.avatar && userInfo.avatar.trim() !== '' && userInfo.avatar !== defAvatar && !avatarLoaded && !avatarError && (
                        <div className="absolute inset-0 rounded-full bg-black bg-opacity-10 flex items-center justify-center">
                          <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin opacity-60"></div>
                        </div>
                      )}
                  </button>

                  {/* 订阅状态图标 - 显示在头像左上角 */}
                  {getCurrentAppTier() && getCurrentAppTier() !== 'base' && (
                    <div className="absolute -top-1 -left-1 bg-white rounded-full p-0.5 shadow-sm">
                      {getSubscriptionIcon(getCurrentAppTier(), 18)}
                    </div>
                  )}
                </div>

                {/* 用户菜单弹出框 */}
                {showUserMenu && (
                  <div className="user-menu mt-2 fade-in right-0">
                    {/* 用户信息显示 */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className="w-8 h-8 bg-gradient-to-br rounded-full flex items-center justify-center">
                              <img
                                src={displayAvatar}
                                alt={username}
                                onError={errorImg}
                                className={`w-full h-full object-cover rounded-full transition-opacity duration-300 ${
                                  userInfo?.avatar && !avatarLoaded && !avatarError ? 'opacity-75' : 'opacity-100'
                                }`}
                              />
                          </div>
                          {/* 订阅状态图标 - 显示在菜单头像左上角 */}
                          {getCurrentAppTier() && getCurrentAppTier() !== 'base' && (
                            <div className="absolute -top-1 -left-1 bg-white rounded-full p-0.5 shadow-sm">
                              {getSubscriptionIcon(getCurrentAppTier(), 12)}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{username || t('header.user')}</div>
                          <div className="text-xs text-gray-500">{t('auth.loggedIn')}</div>
                        </div>
                      </div>
                    </div>

                    {/* 菜单项 */}
                    {userMenuItems.map((item) => (
                      <div
                        key={item.action}
                        onClick={() => handleUserMenuClick(item.action)}
                        className={`user-menu-item ${item.danger ? 'danger' : ''}`}
                      >
                        <span>{item.icon}</span>
                        <span>{item.label}</span>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>


      </header>

    </>
  )
}

export default Header
