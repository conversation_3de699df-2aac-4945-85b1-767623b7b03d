# NovaX 案例展示页面 - SSR版本

这是一个使用Next.js构建的服务端渲染（SSR）版本的案例展示页面，专为SEO优化和更好的用户体验而设计。

## 🚀 主要特性

### SEO优化
- ✅ **服务端渲染** - 内容在服务端预渲染，搜索引擎可以直接抓取
- ✅ **静态内容后备** - 使用`<noscript>`标签提供JavaScript禁用时的内容
- ✅ **结构化数据** - 包含JSON-LD格式的结构化数据
- ✅ **Meta标签优化** - 动态生成title、description等SEO标签
- ✅ **语义化HTML** - 使用语义化的HTML结构

### 用户体验
- ✅ **打字机效果** - AI回答的逐字显示动画
- ✅ **响应式设计** - 支持桌面端和移动端
- ✅ **模式切换** - 可以在"查看过程"和"查看结果"之间切换
- ✅ **Markdown渲染** - 支持丰富的Markdown格式
- ✅ **错误处理** - 优雅的错误页面和加载状态

### 技术特性
- ✅ **TypeScript** - 完整的类型安全
- ✅ **Next.js 13+** - 最新的Next.js特性
- ✅ **Tailwind CSS** - 现代化的CSS框架
- ✅ **组件化** - 可复用的React组件
- ✅ **环境配置** - 支持多环境配置

## 📁 项目结构

```
├── pages/
│   └── case/
│       └── [caseId].tsx          # 动态路由页面
├── components/
│   └── AiResponseRenderer.tsx    # Markdown渲染组件
├── styles/
│   └── case.module.css          # 页面专用样式
├── .env.local                   # 环境变量配置
├── next.config.js              # Next.js配置
└── README-SSR.md              # 本文档
```

## 🛠️ 安装和运行

### 1. 安装依赖

```bash
npm install
# 或
yarn install
```

### 2. 配置环境变量

复制并编辑 `.env.local` 文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local`：

```env
# API配置
API_BASE_URL=http://**************:48081

# 应用配置
NEXT_PUBLIC_APP_NAME=NovaX Base
NEXT_PUBLIC_APP_ICON=https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png
```

### 3. 运行开发服务器

```bash
npm run dev
# 或
yarn dev
```

访问 `http://localhost:3000/case/your-case-id`

### 4. 构建生产版本

```bash
npm run build
npm run start
# 或
yarn build
yarn start
```

## 🔗 URL格式

### 新格式（推荐）
```
/case/[caseId]
```

### 兼容旧格式
```
/case-example.html → 重定向到 /case/demo-001
/case/[caseId].html → 重写到 /case/[caseId]
```

## 📊 API数据格式

服务端期望的API响应格式：

```json
[
  {
    "question": "案例标题",
    "answer": "[{\"query\":\"用户问题\",\"answer\":\"AI回答\"}]"
  }
]
```

其中 `answer` 字段是JSON字符串，包含对话数组。

## 🎨 自定义配置

### 修改应用信息

在 `.env.local` 中修改：

```env
NEXT_PUBLIC_APP_NAME=你的应用名称
NEXT_PUBLIC_APP_ICON=你的图标URL
```

### 修改API端点

在 `.env.local` 中修改：

```env
API_BASE_URL=https://your-api-domain.com
```

### 自定义样式

编辑 `styles/case.module.css` 或 `tailwind.config.js`

## 🔍 SEO优化说明

### 1. 服务端渲染
- 内容在服务端预渲染，搜索引擎可以直接抓取完整内容
- 首屏加载速度更快，用户体验更好

### 2. 静态内容后备
```html
<noscript>
  <!-- JavaScript禁用时显示的静态内容 -->
  <div class="space-y-6">
    <h2>AI对话案例</h2>
    <!-- 预渲染的对话内容 -->
  </div>
</noscript>
```

### 3. 结构化数据
```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "案例标题",
  "description": "案例描述"
}
```

### 4. Meta标签
- 动态生成title、description
- Open Graph标签支持社交媒体分享
- 适当的缓存策略

## 🚀 部署

### Vercel部署（推荐）

1. 连接GitHub仓库到Vercel
2. 设置环境变量
3. 自动部署

### 自定义服务器部署

```bash
npm run build
npm run start
```

### Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 测试SEO效果

### 1. Google Search Console
- 使用"URL检查"工具
- 查看"已渲染的HTML"

### 2. 在线工具
- [Google Mobile-Friendly Test](https://search.google.com/test/mobile-friendly)
- [Rich Results Test](https://search.google.com/test/rich-results)

### 3. 模拟爬虫测试
```bash
# 测试静态内容
curl -A "Googlebot" http://localhost:3000/case/demo-001

# 测试渲染后内容
npx puppeteer-cli print http://localhost:3000/case/demo-001
```

## 📈 性能优化

- ✅ 图片优化（Next.js Image组件）
- ✅ 代码分割（自动）
- ✅ 静态资源缓存
- ✅ 服务端缓存（可配置）
- ✅ 压缩和优化

## 🤝 贡献

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License
