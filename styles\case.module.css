/* 案例页面专用样式 */

.typewriterContainer {
  position: relative;
}

.typewriter<PERSON>ursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 隐藏JavaScript内容当JS禁用时 */
.jsOnly {
  display: block;
}

/* 当JavaScript禁用时显示静态内容 */
@media (scripting: none) {
  .jsOnly {
    display: none;
  }
}

/* 确保noscript内容在JS启用时隐藏 */
.jsEnabled noscript {
  display: none;
}

/* 消息样式 */
.messageContainer {
  margin: 0 auto;
  max-width: 64rem;
  position: relative;
  margin-bottom: 1.5rem;
}

.userMessage {
  margin-left: auto;
  max-width: 32rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  border-bottom-right-radius: 0.375rem;
  padding: 1rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.userMessage:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.assistantMessage {
  margin-right: auto;
  max-width: 64rem;
}

.messageContent {
  font-size: 1rem;
  color: #374151;
  line-height: 1.625;
}

.proseContent {
  font-size: 1rem;
  color: #374151;
  line-height: 1.625;
  max-width: none;
}

/* 头部样式 */
.mobileHeader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.desktopHeader {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 1.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.headerContent {
  max-width: 64rem;
  margin: 0 auto;
}

.backButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  margin-right: 0.75rem;
}

.backButton:hover {
  background-color: #f3f4f6;
}

.appIcon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.appIconImage {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
}

.appName {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  background-color: #3b82f6;
  color: white;
  font-size: 0.875rem;
  border-radius: 0.75rem;
  transition: all 0.2s;
}

.toggleButton:hover {
  background-color: #2563eb;
}

.desktopToggleButton {
  padding: 0.5rem 1rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.desktopToggleButton:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 消息列表区域 */
.messagesArea {
  flex: 1;
  padding: 1.5rem 1rem;
  padding-bottom: 12rem;
  overflow-y: auto;
  background-color: #f9fafb;
  padding-top: 4rem;
}

@media (min-width: 768px) {
  .messagesArea {
    padding-top: 1.5rem;
  }
}

.messagesContainer {
  max-width: 64rem;
  margin: 0 auto;
}

/* 错误页面样式 */
.errorContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
}

.errorContent {
  text-align: center;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 1rem;
}

.errorMessage {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.errorButton {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.errorButton:hover {
  background-color: #2563eb;
}

/* 静态内容样式（noscript） */
.staticContent {
  margin-bottom: 1.5rem;
}

.staticTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.staticQaPair {
  margin-bottom: 1rem;
}

.staticQuery {
  margin-left: auto;
  max-width: 32rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.staticAnswer {
  margin-right: auto;
  max-width: 64rem;
  margin-bottom: 1rem;
}

.staticAnswerContent {
  color: #374151;
  max-width: none;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .desktopHeader {
    display: none;
  }
  
  .userMessage {
    max-width: 28rem;
  }
}

@media (min-width: 768px) {
  .mobileHeader {
    display: none;
  }
}
