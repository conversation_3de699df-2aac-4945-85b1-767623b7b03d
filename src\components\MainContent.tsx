import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import FileUpload, { FileUploadRef } from './file-upload/FileUpload'
import FileIcon from './file-upload/FileIcon'
import Tooltip from './tool-tip/Tooltip'
import { useFileUpload } from './file-upload/useFileUpload'
import { IGetAiAppInfoResponse, PackageByKey } from '../api/src/xai-api';
import { DifyApi, IGetAppParametersResponse } from '../api/src/dify-api';
import { XAiApi } from '../api/src/xai-api';
import { FileTypeMap } from '../api/src/utils/file-type';

import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks';
import { message, Modal } from 'antd'
import { useTranslation } from 'react-i18next'
import { VIPIcon, DiamondCrownIcon } from './icons/Icons'


interface MainContentProps {
  onAppSelect: (appUuid: string) => void
  difyApi: DifyApi
  xAiApi: XAiApi
  appList: IGetAiAppInfoResponse[]
  currentApp: IGetAiAppInfoResponse
  appParam?: IGetAppParametersResponse
  appListSub: Map<string, PackageByKey | null>
  preCheck: () => boolean
}

// 定义数据类型
interface ConfigData {
  icon: string;
  title: string;
  desc: string;
  id: string;
}

// 辅助函数：获取应用按钮样式
const getAppButtonStyles = (app: IGetAiAppInfoResponse, currentApp: IGetAiAppInfoResponse): string => {
  const isDisabled = !app.appNameEn.includes('base')
  const isSelected = currentApp.appUuid === app.appUuid

  if (isDisabled) {
    return 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
  }

  if (isSelected) {
    return 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:shadow-sm'
  }

  return 'bg-white text-gray-700 dark:text-gray-200 hover:shadow-sm hover:bg-gray-50 dark:hover:bg-gray-800'
}

// 辅助函数：获取应用标签文本
const getAppLabelText = (app: IGetAiAppInfoResponse, t: (key: string) => string): string => {
  return app.appNameEn.includes('base') ? t('common.free') : t('common.expert')
}

// 辅助函数：获取应用名称样式
const getAppNameStyles = (app: IGetAiAppInfoResponse, currentApp: IGetAiAppInfoResponse): string => {
  const baseStyles = 'text-sm font-medium text-center'
  const selectedStyles = currentApp.appUuid === app.appUuid ? 'text-blue-600' : 'text-gray-700'
  return `${baseStyles} ${selectedStyles}`
}

// 辅助函数：检查应用是否已订阅
const isAppSubscribed = (appNameEn: string, appListSub: Map<string, any>): boolean => {
  const subStatus = appListSub.get(appNameEn)?.subStatus
  return subStatus === 1 || subStatus === 3
}

// 辅助函数：获取订阅等级类型
const getSubscriptionTier = (appNameEn: string, appListSub: Map<string, any>): 'base' | 'pro' | 'ultra' | null => {
  const subData = appListSub.get(appNameEn)
  if (!subData || !isAppSubscribed(appNameEn, appListSub)) return null

  if (appNameEn.includes('base')) return 'base'
  if (appNameEn.includes('pro')) return 'pro'
  if (appNameEn.includes('ultra')) return 'ultra'

  return null
}

// 辅助函数：根据等级获取订阅状态图标
const getSubscriptionIcon = (tier: 'base' | 'pro' | 'ultra' | null, size: number = 24) => {
  if (!tier) return null

  const iconProps = { width: size, height: size }

  switch (tier) {
    case 'base':
      return null // Base版本不显示任何图标
    case 'pro':
      return <VIPIcon {...iconProps} />
    case 'ultra':
      return <DiamondCrownIcon {...iconProps} />
    default:
      return null
  }
}

// 订阅状态图标组件 - 显示订阅等级图标
const SubscriptionStatusIcon: React.FC<{
  appNameEn: string;
  appListSub: Map<string, any>
}> = ({ appNameEn, appListSub }) => {
  const tier = getSubscriptionTier(appNameEn, appListSub)
  if (!tier || tier === 'base') return null // Base版本不显示任何图标

  const icon = getSubscriptionIcon(tier, 20) // 从16调整到20像素
  if (!icon) return null

  return (
    <div
      className="absolute top-1 left-1"
      style={{
        borderRadius: '6px'
      }}
    >
      {icon}
    </div>
  )
}

// 应用类型标签组件
const AppTypeLabel: React.FC<{
  app: IGetAiAppInfoResponse
  currentApp: IGetAiAppInfoResponse
  t: (key: string) => string
}> = ({ app, currentApp, t }) => {
  return (
    <div
      className={`
        absolute top-0 right-0 text-white text-xs px-2 py-1
        ${currentApp.appUuid === app.appUuid ? 'bg-blue-500' : 'bg-gray-400'}
      `}
      style={{
        borderTopRightRadius: '12px',
        borderBottomLeftRadius: '8px',
        fontSize: '11px',
        lineHeight: '1.2'
      }}
    >
      {getAppLabelText(app, t)}
    </div>
  )
}

// 桌面端应用卡片组件
const AppCard: React.FC<{
  app: IGetAiAppInfoResponse
  currentApp: IGetAiAppInfoResponse
  appListSub: Map<string, any>
  onAppSelect: (appUuid: string) => void
  t: (key: string) => string
}> = ({ app, currentApp, appListSub, onAppSelect, t }) => {
  return (
    <button
      onClick={() => {
        if (!app.appNameEn.includes('base')) {
          message.info(t('chat.comingSoon'))
          console.log('暂时不能对外开放')
          return
        } else {
          onAppSelect(app.appUuid)
        }
      }}
      className={`
        relative p-4 rounded-xl border transition-all duration-200
        ${getAppButtonStyles(app, currentApp)}
      `}
    >
      <SubscriptionStatusIcon appNameEn={app.appNameEn} appListSub={appListSub} />
      <AppTypeLabel app={app} currentApp={currentApp} t={t} />

      <div className="flex justify-center mb-2 mt-1">
        <img src={app.appIcon} alt={app.appName} className="w-8 h-8" />
      </div>

      <div className={getAppNameStyles(app, currentApp)}>
        {app.appName}
      </div>
    </button>
  )
}

// 移动端应用卡片组件
const MobileAppCard: React.FC<{
  app: IGetAiAppInfoResponse
  currentApp: IGetAiAppInfoResponse
  appListSub: Map<string, any>
  onAppSelect: (appUuid: string) => void
  t: (key: string) => string
}> = ({ app, currentApp, appListSub, onAppSelect, t }) => {
  const isSelected = currentApp.appUuid === app.appUuid
  const isDisabled = !app.appNameEn.includes('base')

  return (
    <button
      onClick={() => {
        if (!app.appNameEn.includes('base')) {
          message.info(t('chat.comingSoon'))
          console.log('暂时不能对外开放')
          return
        } else {
          onAppSelect(app.appUuid)
        }
      }}
      className={`
        relative p-3 rounded-xl border transition-all duration-200 hover:shadow-sm flex-shrink-0
        ${isDisabled ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' : 'bg-white'}
        ${isSelected
          ? 'border-blue-200 bg-blue-25 shadow-sm'
          : 'border-gray-100 hover:border-gray-200'
        }
      `}
      style={{
        width: '160px',
        ...(isSelected ? { backgroundColor: '#f0f7ff' } : {})
      }}
    >
      <SubscriptionStatusIcon appNameEn={app.appNameEn} appListSub={appListSub} />

      {/* 移动端专用的应用类型标签 */}
      <div
        className={`absolute top-0 right-0 text-white text-xs px-1.5 py-0.5 ${
          isSelected ? 'bg-blue-500' : 'bg-gray-400'
        }`}
        style={{
          borderTopRightRadius: '12px',
          borderBottomLeftRadius: '6px',
          fontSize: '10px',
          lineHeight: '1.2'
        }}
      >
        {getAppLabelText(app, t)}
      </div>

      <div className="flex justify-center mb-2 mt-1">
        <img src={app.appIcon} alt={app.appName} className="w-7 h-7" />
      </div>

      <div className={`text-sm font-medium text-center ${
        isSelected ? 'text-blue-600' : 'text-gray-700'
      }`}>
        {app.appName}
      </div>
    </button>
  )
}

const MainContent = ({ onAppSelect, difyApi, xAiApi, appList, currentApp, appParam, appListSub, preCheck }: MainContentProps) => {
  const navigate = useNavigate()
  const { currentLanguage, navigateToApp } = useI18nRouter()
  const { t } = useSimpleTranslation()
  const { i18n } = useTranslation()

  // 根据应用名称获取国际化描述
  const getAppDescription = (appNameEn: string): string => {
    switch (appNameEn) {
      case 'novax-base':
        return t('home.inspirationEngine')
      case 'elavax-base':
        return t('home.analysisDescription')
      default:
        return t('home.description')
    }
  }

  const fileUploadRef = useRef<FileUploadRef>(null)
  const inputAreaRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [inputValue, setInputValue] = useState('')
  const [showSceneMenu, setShowSceneMenu] = useState(false)
  // 文件状态现在由 useFileUpload Hook 管理
  const [selectedSuggestId, setSelectedSuggestId] = useState(t('scene.general'))
  const [suggestedQuestions, setSuggestedQuestions] = useState<ConfigData[]>([])
  const [caseExamples, setCaseExamples] = useState<ConfigData[]>([])
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)
  const [dynamicPlaceholder, setDynamicPlaceholder] = useState(t('chat.inputPlaceholderHome'))
  const subStatusDetail = appListSub.get(currentApp.appNameEn)



  // 监听语言变化，更新placeholder为通用多语言文案
  useEffect(() => {
    setDynamicPlaceholder(t('chat.inputPlaceholderHome'))
  }, [currentLanguage, i18n.language, t])

  // 防抖的配置数据获取函数
  const fetchConfigData = useCallback(async () => {
    if (!currentApp?.appNameEn) return

    setIsLoadingConfig(true)
    try {
        // 获取开场白 - 添加locale参数
        const questionResponse = await xAiApi.getConfigByKeyFromCache({
          configKey: 'suggested_questions',
          locale: currentLanguage
        })
        if (questionResponse && typeof questionResponse === 'object') {
          const questions = questionResponse[currentApp.appNameEn]
          if (questions && Array.isArray(questions)) {
            const filterData = questions.filter(question => question.lang === currentLanguage)
            setSuggestedQuestions(filterData)
            setSelectedSuggestId(filterData[0]?.id)
          } else {
            console.log('No questions found for app:', currentApp.appNameEn)
            setSuggestedQuestions([])
            setSelectedSuggestId('')
          }
        } else {
          console.log('Invalid questionResponse:', questionResponse)
          setSuggestedQuestions([])
          setSelectedSuggestId('')
        }

        // 获取案例数据 - 添加locale参数
        const caseExamplesResponse = await xAiApi.getConfigByKeyFromCache({
          configKey: 'case_examples',
          locale: currentLanguage
        })
        if (caseExamplesResponse && typeof caseExamplesResponse === 'object') {
          const appCaseExamples = caseExamplesResponse[currentApp.appNameEn]
          if (appCaseExamples && Array.isArray(appCaseExamples)) {
            setCaseExamples(appCaseExamples)
          } else {
            console.log('No case examples found for app:', currentApp.appNameEn)
            setCaseExamples([])
          }
        } else {
          console.log('Invalid caseExamplesResponse:', caseExamplesResponse)
          setCaseExamples([])
        }


      } catch (error) {
        console.error('获取配置数据失败:', error)
        // 如果API调用失败，使用默认数据
        setSuggestedQuestions([])
        setSelectedSuggestId('')
        setCaseExamples([])
        setDynamicPlaceholder(t('chat.inputPlaceholderHome'))
      } finally {
        setIsLoadingConfig(false)
      }
    }, [currentApp?.appNameEn, xAiApi, currentLanguage, t])

  // 使用useEffect调用防抖函数
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchConfigData()
    }, 300) // 300ms防抖延迟

    return () => clearTimeout(timeoutId)
  }, [fetchConfigData])

  // 动态获取文件上传数量限制
  const maxFilesLimit = appParam?.file_upload?.number_limits || 2

  // 使用 useFileUpload Hook 管理文件上传
  const {
    uploadedFiles: showFiles,
    uploadData: uploadFiles,
    isUploading,
    handleFilesUploaded,
    removeFile,
    clearFiles
  } = useFileUpload({
    difyApi,
    maxFiles: maxFilesLimit, // 使用动态配置值
    onPreCheck: preCheck,
    onUploadSuccess: (files, uploadData) => {
      console.log('文件上传成功:', files, uploadData)
    }
  })

  // // 监听currentApp变化，清空输入框和上传的附件
  // useEffect(() => {
  //   console.log('应用切换，清空输入框和附件:', currentApp?.appNameEn)
  //   // 清空输入框
  //   setInputValue('')
  //   // 清空上传的附件
  //   clearFiles()
  // }, [currentApp?.appUuid, clearFiles])

  const allowedFileTypes = useMemo(() => {
		if (!appParam?.file_upload.enabled) {
			return []
		}
		const result: string[] = []
		appParam.file_upload.allowed_file_types.forEach(item => {
			if (FileTypeMap.get(item)) {
				result.push(...((FileTypeMap.get(item) as string[]) || []))
			}
		})
		return result
	}, [appParam?.file_upload])

  const handleSubmit = () => {
    if (currentApp.appNameEn.includes('pro')) {
      message.success(t('chat.comingSoon'))
      return
    }

    // 只提交用户输入的部分，不包括应用名称前缀
    const userInput = inputValue.trim()

    if (userInput) {
      const query = userInput

      // 使用sessionStorage存储数据，标记为新对话
      const chatData = {
        query,
        appNameEn: currentApp.appNameEn,
        appUuid: currentApp.appUuid,
        isNewConversation: true,
        files: uploadFiles.length > 0 ? uploadFiles : undefined,
        timestamp: Date.now()
      }

      // 使用固定的key存储初始数据
      sessionStorage.setItem('chatInitialData', JSON.stringify(chatData))

      // 直接跳转到新对话页面（使用"new"标识符）
      const targetUrl = `/${currentLanguage}/${currentApp.appNameEn}/new`
      navigate(targetUrl, { replace: true })
    }
  }

  // 滚动到输入框的函数并自动聚焦
  const scrollToInputArea = () => {
    if (inputAreaRef.current) {
      inputAreaRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })

      // 在滚动完成后检查订阅状态，只有已订阅用户才自动聚焦
      // 使用setTimeout确保滚动动画完成后再执行聚焦检查
      setTimeout(() => {
        // 检查用户是否已订阅，只有已订阅用户才自动聚焦
        // 使用preCheck()函数进行完整的前置检查（包括登录和订阅状态）
        const isUserSubscribed = preCheck()

        if (isUserSubscribed && textareaRef.current) {
          textareaRef.current.focus()
          // 将光标移动到文本末尾
          const length = textareaRef.current.value.length
          textareaRef.current.setSelectionRange(length, length)
        }
        // 如果用户未订阅，只执行滚动，不执行聚焦
        // 这样可以避免在订阅弹窗显示时输入框仍然保持聚焦状态
      }, 800) // 800ms延迟确保滚动动画完成
    }
  }



  const handleSceneSelect = (id: string, title: string) => {
    if (!inputValue.trim()) {
        setInputValue(title)
        setSelectedSuggestId(id) // 更新选中的场景标题
        setShowSceneMenu(false)
        return
    }
    // 使用 Modal.confirm 添加确认提示
    Modal.confirm({
      title: t('scene.selectTitle'),
      content: t('scene.selectConfirmMessage'),
      onOk() {
        setInputValue(title)
        setSelectedSuggestId(id) // 更新选中的场景标题
        setShowSceneMenu(false)
      },
      onCancel() {
        // 用户取消，不执行任何操作
      },
    })
  }

  // 处理案例卡片点击事件
  const handleCaseClick = (caseId: string) => {
    navigate(`/${currentLanguage}/cases/${caseId}`)
  };

  return (
    <div className="flex-1 flex flex-col" style={{ backgroundColor: 'var(--bg-main)' }}>
      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col pt-4 px-4 md:pt-6 md:px-6 lg:pt-8 lg:px-8">
        <div className="flex flex-col items-center justify-start">
        <div className="w-full max-w-6xl">
          {/* 主标题区域 */}
          <div className="text-center mb-6 md:mb-8">
            <div className="flex flex-col items-center gap-2 mb-3">
              <div className="flex items-center gap-3">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 flex-1 min-w-0">~{t('home.welcomePrefix')} {currentApp.appName}</h2>
                <div className="w-8 h-8 min-w-[32px] min-h-[32px] flex items-center justify-center flex-shrink-0">
                  <img src={currentApp.appIcon} alt={currentApp.appName} className="w-8 h-8 object-contain" />
                </div>
              </div>
            </div>
            <p className="text-sm md:text-lg text-gray-600 mb-6 md:mb-10">
              {getAppDescription(currentApp.appNameEn)}
            </p>
          </div>

          {/* 重新设计的输入框区域 */}
          <FileUpload
            ref={fileUploadRef}
            onFilesUploaded={handleFilesUploaded}
            allowedFileTypes={allowedFileTypes}
            disabled={!appParam?.file_upload.enabled}
            isLoading={isUploading}
            maxFiles={maxFilesLimit} // 使用动态配置值
            currentFileCount={showFiles.length} // 传递当前已上传文件数量
          >
            <div ref={inputAreaRef} className="relative bg-white shadow-xl mb-6 md:mb-8 rounded-2xl border border-gray-200 hover:shadow-2xl transition-shadow duration-300 ease-in-out">
              {/* 文本输入区域 - 应用名称嵌入到文本中 */}
              <div className="pt-4 px-4">
                <div className="relative overflow-hidden">
                  {/* 完整覆盖层容器 */}
                  <div
                    className="absolute pointer-events-none z-10"
                    style={{
                      top: '0px',
                      left: '0px',
                      right: '0px',
                      bottom: '0px',
                      background: 'white', // 完全覆盖下面的内容
                      transform: `translateY(${-(textareaRef.current?.scrollTop || 0)}px)`,
                      height: '24px' // 只覆盖第一行的高度
                    }}
                  >
                    {/* 图标 */}
                    <img
                      src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png"
                      className="w-4 h-4 absolute"
                      style={{
                        top: '2px',
                        left: '0px'
                      }}
                      alt="AI"
                    />

                    {/* 应用名称 */}
                    <span
                      className="absolute"
                      style={{
                        top: '0px',
                        left: '24px', // 图标 + 空格的宽度
                        fontSize: '16px',
                        lineHeight: '1.6',
                        color: '#2563eb', // 蓝色
                        fontWeight: '400'
                      }}
                    >
                      {currentApp.appName}
                    </span>
                  </div>

                  <textarea
                    ref={textareaRef}
                    value={`🤖 ${currentApp.appName} ${inputValue}`}
                    onChange={(e) => {
                      const content = e.target.value
                      const appPrefix = `🤖 ${currentApp.appName} `

                      // 如果用户删除了应用名称前缀，重新添加
                      if (!content.startsWith(appPrefix)) {
                        setInputValue('')
                      } else {
                        // 提取用户输入的部分（去掉应用名称前缀）
                        const userInput = content.slice(appPrefix.length)
                        setInputValue(userInput)
                      }
                    }}
                    onKeyDown={(e) => {
                      const textarea = e.target as HTMLTextAreaElement
                      const cursorPosition = textarea.selectionStart
                      const appPrefix = `🤖 ${currentApp.appName} `

                      // 防止用户编辑应用名称部分
                      if (cursorPosition < appPrefix.length) {
                        // 如果光标在应用名称部分，移动到用户输入区域
                        setTimeout(() => {
                          textarea.setSelectionRange(appPrefix.length, appPrefix.length)
                        }, 0)
                        return
                      }

                      // 处理回车键
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        handleSubmit()
                      }
                    }}
                    onFocus={(e) => {
                      if (!preCheck()) return

                      // 确保光标在用户输入区域
                      const textarea = e.target as HTMLTextAreaElement
                      const appPrefix = `🤖 ${currentApp.appName} `
                      setTimeout(() => {
                        if (textarea.selectionStart < appPrefix.length) {
                          textarea.setSelectionRange(appPrefix.length, appPrefix.length)
                        }
                      }, 0)
                    }}
                    onScroll={() => {
                      // 更新所有覆盖层位置
                      const scrollTop = textareaRef.current?.scrollTop || 0
                      const overlays = document.querySelectorAll('.absolute.pointer-events-none.z-10') as NodeListOf<HTMLElement>
                      overlays.forEach(overlay => {
                        overlay.style.transform = `translateY(${-scrollTop}px)`
                      })
                    }}

                    className="w-full border-0 resize-none focus:outline-none focus:border-transparent text-gray-800 placeholder-gray-500 transition-all duration-300 ease-in-out"
                    style={{
                      padding: '0',
                      minHeight: '140px',
                      fontSize: '16px',
                      lineHeight: '1.6',
                      outline: 'none',
                      border: 'none',
                      boxShadow: 'none',
                      background: 'transparent'
                    }}
                    placeholder={`🤖 ${currentApp.appName} ${dynamicPlaceholder}`}
                  />
                </div>
              </div>

              {/* 已上传文件显示 */}
              {showFiles.length > 0 && (
                <div className="px-6 pb-4 pt-2">
                  <div className="flex flex-wrap gap-3">
                    {showFiles.map((file, index) => (
                      <Tooltip
                        key={`${file.name}-${index}`}
                        content={file.name}
                        position="top"
                        delay={300}
                      >
                        <div className="relative group">
                          <div className="flex items-center justify-center w-11 h-11 bg-gray-50 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer">
                            <FileIcon fileName={file.name} size="md" />
                            <button
                              onClick={() => removeFile(index)}
                              className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center opacity-100 md:opacity-0 md:group-hover:opacity-100"
                            >
                              ✕
                            </button>
                          </div>
                        </div>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}

              {/* 简化的底部工具栏 - 单排设计 */}
              <div className="flex items-center justify-between p-2 border-gray-100">
                <div className="flex items-center gap-3">
                  {/* 场景选择 */}
                  <div className="relative">
                    <button
                      onClick={() => setShowSceneMenu(!showSceneMenu)}
                      className="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md scene-select-button" // 添加 scene-select-button 类
                    >
                      <img
                        src={suggestedQuestions.find(s => s.id === selectedSuggestId)?.icon}
                        className="w-4 h-4 object-contain"
                      />
                      <span className="text-sm hidden md:inline">{suggestedQuestions.find(s => s.id === selectedSuggestId)?.title}</span>
                      <span className={`text-xs transform transition-transform duration-200`}>
                        <svg className={`w-3 h-3 text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 transition-transform ${showSceneMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                    </button>

                    {/* 移动端全屏场景选择弹出框 */}
                    {showSceneMenu && (
                      <div className="md:hidden mobile-scene-popup fade-in">
                        {/* 移动端头部 */}
                        <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                          <h3 className="text-xl font-semibold text-gray-900">{t('scene.selectType')}</h3>
                          <button
                            onClick={() => setShowSceneMenu(false)}
                            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg"
                          >
                            ✕
                          </button>
                        </div>

                        {/* 移动端场景网格 */}
                        <div className="mobile-scene-grid">
                          {isLoadingConfig ? (
                            <div className="flex items-center justify-center py-4">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                              <span className="ml-2 text-sm text-gray-500">{t('common.loading')}</span>
                            </div>
                          ) : (
                            suggestedQuestions.map((item) => (
                              <div
                                key={item.title}
                                onClick={() => handleSceneSelect(item.id, item.title)}
                                className="mobile-scene-category scene-category"
                              >
                                <div className="scene-icon">
                                  <img
                                    src={item.icon}
                                    alt={item.title}
                                    className="w-8 h-8 object-contain"
                                  />
                                </div>
                                <div className="scene-title text-lg font-semibold">{item.title}</div>
                                <div className="scene-desc text-base text-gray-600 leading-relaxed">{item.desc}</div>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    )}

                    {/* 桌面端场景弹出框 */}
                    {showSceneMenu && (
                      <div className="hidden md:block scene-popup fade-in">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {isLoadingConfig ? (
                            <div className="col-span-full flex items-center justify-center py-8">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                              <span className="ml-2 text-sm text-gray-500">{t('common.loading')}</span>
                            </div>
                          ) : (
                            suggestedQuestions.map((item) => (
                              <div
                                key={item.title}
                                onClick={() => handleSceneSelect(item.id, item.title)}
                                className="scene-category"
                              >
                                <div className="scene-icon">
                                  <img
                                    src={item.icon}
                                    alt={item.title}
                                    className="w-12 h-12 object-contain"
                                  />
                                </div>
                                <div className="scene-title">{item.title}</div>
                                <div className="scene-desc md:hidden">{item.desc}</div>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 文件上传 - 唯一入口 */}
                  {appParam?.file_upload.enabled && (
                  <Tooltip
                    content={
                      <div className="text-center space-y-1.5">
                        <div className="text-xs text-gray-600">
                          <div className="font-medium mb-1">
                            {t('fileUpload.maxFiles').replace('{count}', maxFilesLimit.toString())}
                          </div>
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500 leading-relaxed">
                              {t('fileUpload.supportedFormats')}: {allowedFileTypes.join(', ')}
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                    position="bottom"
                    delay={300}
                  >
                    <button
                      className="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 cursor-pointer transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md"
                      onClick={() => {
                        if (!preCheck()) return
                        fileUploadRef.current?.triggerFileSelect()
                      }}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                      <span className="hidden md:inline">{t('chat.uploadFile')}</span>
                    </button>
                  </Tooltip>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-xs text-gray-400 hidden sm:block">
                    Enter {t('chat.send')}
                  </div>
                  <button
                    onClick={handleSubmit}
                    disabled={!inputValue.trim() && showFiles.length === 0}
                    className="relative flex items-center justify-center w-10 h-10 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed group shadow-lg hover:shadow-xl transition-all duration-200 ease-out transform hover:scale-105 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-400 hover:to-blue-400 focus:ring-purple-500 hover:shadow-purple-500/30"
                  >
                    <div className="flex items-center justify-center">
                      {/* 发送图标 */}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="group-hover:animate-[takeoff_1.5s_ease-out_infinite] transition-transform duration-200"
                      >
                        <path d="M22 2L11 13"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </FileUpload>

          {/* 智能体模式选择器 - 移动端每行2个，支持水平滑动 */}
          <div className="mb-8">
            {/* 移动端：水平滑动布局 */}
            <div className="md:hidden">
              <div className="overflow-x-auto scrollbar-hide">
                <div className="flex space-x-3 px-4 pb-2" style={{ width: 'max-content' }}>
                  {appList.map((app) => (
                    <MobileAppCard
                      key={app.appUuid}
                      app={app}
                      currentApp={currentApp}
                      appListSub={appListSub}
                      onAppSelect={onAppSelect}
                      t={t}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* 桌面端：网格布局 */}
            <div className="hidden md:grid grid-cols-5 gap-4 px-0">
              {appList.map((app) => (
                <AppCard
                  key={app.appUuid}
                  app={app}
                  currentApp={currentApp}
                  appListSub={appListSub}
                  onAppSelect={onAppSelect}
                  t={t}
                />
              ))}
            </div>
          </div>



          {/* 用户案例展示区域 - 移动端优化 */}
          <div className="mb-6 md:mb-8 px-3 md:px-0">
            <div className="text-center mb-4 md:mb-6">
              <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-2">{t('home.caseShowcase')}</h3>
              <p className="text-xs md:text-sm text-gray-600">{t('home.caseDescription')}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6">
              {caseExamples.map((caseItem) => (
                <div 
                  key={caseItem.title} 
                  className="case-card" 
                  onClick={() => handleCaseClick(caseItem.id)} // 修改此处，调用 handleCaseClick
                >
                  <div className="case-card-image h-52 md:h-40">
                    <img
                      src={caseItem.icon}
                      alt={caseItem.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">{caseItem.title}</h4>
                  <p className="text-xs text-gray-600 leading-relaxed">{caseItem.desc}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 示例和提示 */}
          <div className="text-center text-gray-500 text-xs md:text-sm pb-6 md:pb-8 px-4 md:px-0">
            <p
              onClick={scrollToInputArea}
              className="cursor-pointer hover:text-blue-600 transition-all duration-300 hover:scale-105 transform"
            >
              {t('home.agentModeDescription')}
            </p>
          </div>
          </div>
        </div>
      </div>

      {/* 点击外部关闭场景菜单 */}
      {showSceneMenu && (
        <div
          className="fixed inset-0 z-20"
          onClick={() => setShowSceneMenu(false)}
        />
      )}

    </div>
  )
}

export default MainContent
